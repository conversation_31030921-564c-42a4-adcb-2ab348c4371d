# 渠道图片资源说明

## 目录结构
```
public/imgs/channel/
├── tg.png          # Telegram 图标
├── whatsapp.png    # WhatsApp 图标 (待添加)
├── wechat.png      # 微信图标 (待添加)
└── ...
```

## 使用方式

### 1. 静态引用（推荐用于固定资源）
```vue
<el-image src="/imgs/channel/tg.png" />
```

### 2. 动态引用（推荐用于数据驱动）
```vue
<template>
  <el-image :src="channel.icon" />
</template>

<script setup>
const channels = ref([
  {
    name: 'Telegram',
    icon: '/imgs/channel/tg.png'
  },
  {
    name: 'WhatsApp', 
    icon: '/imgs/channel/whatsapp.png'
  }
])
</script>
```

## 优势
- ✅ 无需重新编译代码
- ✅ 支持动态添加新渠道
- ✅ 图片路径简洁明了
- ✅ 便于维护和扩展