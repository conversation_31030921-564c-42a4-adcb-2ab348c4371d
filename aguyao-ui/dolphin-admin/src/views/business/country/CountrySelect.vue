<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择国家"
    width="800px"
    :before-close="handleClose"
  >
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="简码" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入简码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="国家名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入国家名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> 重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <el-table 
      v-loading="loading" 
      :data="list" 
      :stripe="true" 
      :show-overflow-tooltip="true"
      @row-click="handleRowClick"
      highlight-current-row
    >
      <el-table-column label="选择" width="80" align="center">
        <template #default="scope">
          <el-radio 
            v-model="selectedCountryId" 
            :label="scope.row.id" 
            @change="handleRadioChange(scope.row)"
          >
            &nbsp;
          </el-radio>
        </template>
      </el-table-column>
      <el-table-column label="Id" align="center" prop="id" />
      <el-table-column label="简码" align="center" prop="code" />
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column label="描述" align="center" prop="remark" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180px" :formatter="dateFormatter" />
    </el-table>
    
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="!selectedCountry">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { CountryApi, CountryVO } from '@/api/business/country'
import { dateFormatter } from '@/utils/formatTime'

/** 国家选择Dialog */
defineOptions({ name: 'CountrySelect' })

// 定义组件的props
interface Props {
  modelValue?: boolean
}

// 定义组件的emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', country: CountryVO): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})

const emit = defineEmits<Emits>()

const loading = ref(true) // 列表的加载中
const list = ref<CountryVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const selectedCountry = ref<CountryVO | null>(null) // 选中的国家
const selectedCountryId = ref<number | null>(null) // 选中的国家ID
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: undefined,
  name: undefined,
  remark: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单

// 控制弹窗显示
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await CountryApi.getCountryPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 行点击事件 */
const handleRowClick = (row: CountryVO) => {
  selectedCountry.value = row
  selectedCountryId.value = row.id || null
}

/** 单选按钮选择事件 */
const handleRadioChange = (row: CountryVO) => {
  selectedCountry.value = row
}

/** 关闭弹窗 */
const handleClose = () => {
  dialogVisible.value = false
  selectedCountry.value = null
  selectedCountryId.value = null
  // 重置搜索条件
  queryFormRef.value?.resetFields()
  queryParams.pageNo = 1
}

/** 确认选择 */
const handleConfirm = () => {
  if (selectedCountry.value) {
    emit('confirm', selectedCountry.value)
    handleClose()
  }
}

/** 打开弹窗 */
const open = () => {
  dialogVisible.value = true
  getList()
}

// 监听弹窗打开
watch(dialogVisible, (newVal) => {
  if (newVal) {
    getList()
  }
})

// 暴露方法给父组件
defineExpose({
  open
})
</script>

<style scoped>
.dialog-footer {
  text-align: left;
}

:deep(.el-table__row) {
  cursor: pointer;
}

:deep(.el-table__row:hover) {
  background-color: var(--el-table-row-hover-bg-color);
}
</style>
