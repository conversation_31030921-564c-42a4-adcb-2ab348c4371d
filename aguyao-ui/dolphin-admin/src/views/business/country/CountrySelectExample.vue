<template>
  <ContentWrap>
    <el-card>
      <template #header>
        <div class="card-header">
          <span>国家选择组件使用示例</span>
        </div>
      </template>
      
      <el-form :model="form" label-width="120px">
        <el-form-item label="选择的国家：">
          <el-input 
            v-model="selectedCountryDisplay" 
            placeholder="请选择国家" 
            readonly
            style="width: 300px;"
          >
            <template #append>
              <el-button @click="openCountrySelect" type="primary">
                <Icon icon="ep:search" /> 选择
              </el-button>
            </template>
          </el-input>
        </el-form-item>
        
        <el-form-item label="国家ID：">
          <el-input v-model="form.countryId" disabled style="width: 300px;" />
        </el-form-item>
        
        <el-form-item label="国家简码：">
          <el-input v-model="form.countryCode" disabled style="width: 300px;" />
        </el-form-item>
        
        <el-form-item label="国家名称：">
          <el-input v-model="form.countryName" disabled style="width: 300px;" />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 国家选择弹窗 -->
    <CountrySelect 
      v-model="countrySelectVisible" 
      @confirm="handleCountrySelect" 
    />
  </ContentWrap>
</template>

<script setup lang="ts">
import { CountryVO } from '@/api/business/country'
import CountrySelect from './CountrySelect.vue'

/** 国家选择组件使用示例 */
defineOptions({ name: 'CountrySelectExample' })

const message = useMessage() // 消息弹窗

// 表单数据
const form = reactive({
  countryId: '',
  countryCode: '',
  countryName: ''
})

// 国家选择弹窗显示状态
const countrySelectVisible = ref(false)

// 选中的国家显示文本
const selectedCountryDisplay = computed(() => {
  if (form.countryName && form.countryCode) {
    return `${form.countryName} (${form.countryCode})`
  }
  return ''
})

/** 打开国家选择弹窗 */
const openCountrySelect = () => {
  countrySelectVisible.value = true
}

/** 处理国家选择确认 */
const handleCountrySelect = (country: CountryVO) => {
  form.countryId = country.id?.toString() || ''
  form.countryCode = country.code || ''
  form.countryName = country.name || ''
  message.success(`已选择国家：${country.name}`)
}

/** 提交表单 */
const handleSubmit = () => {
  if (!form.countryId) {
    message.warning('请先选择国家')
    return
  }
  
  console.log('提交的表单数据：', form)
  message.success('提交成功！')
}

/** 重置表单 */
const handleReset = () => {
  form.countryId = ''
  form.countryCode = ''
  form.countryName = ''
  message.info('表单已重置')
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>