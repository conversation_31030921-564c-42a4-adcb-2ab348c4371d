<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="50%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-form-item label="中文标识" prop="taskTypeCn">
        <el-input
          v-model="formData.taskTypeCn"
          placeholder="请输入任务类型中文标识"
        />
      </el-form-item>
      <el-form-item label="英文标识" prop="taskType">
        <el-select
          v-model="formData.taskType"
          placeholder="请选择任务类型英文标识"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="图片上传" prop="img">
        <el-upload
          class="upload-demo"
          :action="uploadAction"
          :headers="uploadHeaders"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :file-list="fileList"
          list-type="picture-card"
          :limit="1"
          accept="image/*"
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
      </el-form-item>
      
      <el-form-item label="是否开启" prop="isOpen">
        <el-radio-group v-model="formData.isOpen">
          <el-radio label="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="跳转路径" prop="jumpPath">
        <el-input
          v-model="formData.jumpPath"
          placeholder="请输入跳转路径（如：/business/tg/AllType/）"
        />
      </el-form-item>
      <el-form-item label="是否置顶" prop="isTop">
        <el-radio-group v-model="formData.isTop">
          <el-radio label="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="基础介绍文本" prop="text">
        <el-input type="textarea" :rows="5" v-model="formData.text" placeholder="请输入Telegram基础介绍文本" />
      </el-form-item>
      <el-form-item label="是否为新功能" prop="isNew">
        <el-radio-group v-model="formData.isNew">
          <el-radio label="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="业务说明文本" prop="businessText">
        <el-input
          type="textarea"
          :rows="5"
          v-model="formData.businessText"
          placeholder="请输入业务说明文本"
        />
      </el-form-item>
      <el-form-item label="积分值" prop="points">
        <el-input-number v-model="formData.points" placeholder="请输入积分值" style="width: 100%;" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { PersonalCenterApi, PersonalCenterVO } from '@/api/business/personalcenter'
import { Plus } from '@element-plus/icons-vue'
import { getAccessToken } from '@/utils/auth'
import type { UploadProps, UploadUserFile } from 'element-plus'

/** 个人中心业务 表单 */
defineOptions({ name: 'PersonalCenterForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  taskTypeCn: undefined,
  img: undefined,
  taskType: undefined,
  isOpen: undefined,
  jumpPath: undefined,
  isTop: undefined,
  text: undefined,
  isNew: undefined,
  businessText: undefined,
  points: undefined
})

// 上传相关配置
const uploadAction = ref(import.meta.env.VITE_BASE_URL + '/admin-api/infra/file/upload')
const uploadHeaders = ref({
  Authorization: 'Bearer ' + getAccessToken()
})
const fileList = ref<UploadUserFile[]>([])
const formRules = reactive({
  taskTypeCn: [
    {
      required: true,
      message: '任务类型中文标识（如：business.TG_All_Check）不能为空',
      trigger: 'blur'
    }
  ],
  img: [{ required: true, message: '图片关联标识（如：tgAll）不能为空', trigger: 'blur' }],
  taskType: [
    { required: true, message: '任务类型英文标识（如：tgSeniorPro）不能为空', trigger: 'change' }
  ],
  isOpen: [{ required: true, message: '是否开启（1=开启，0=关闭）不能为空', trigger: 'blur' }],
  jumpPath: [
    { required: true, message: '跳转路径（如：/business/tg/AllType/）不能为空', trigger: 'blur' }
  ],
  isTop: [{ required: true, message: '是否置顶（1=置顶，0=不置顶）不能为空', trigger: 'blur' }],
  text: [{ required: true, message: 'Telegram基础介绍文本不能为空', trigger: 'blur' }],
  isNew: [
    { required: true, message: '是否为新功能（1=新功能，0=非新功能）不能为空', trigger: 'blur' }
  ],
  businessText: [
    { required: true, message: '业务说明文本（TG全格式筛选相关描述）不能为空', trigger: 'blur' }
  ],
  points: [{ required: true, message: '积分值（如：0.005）不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await PersonalCenterApi.getPersonalCenter(id)
      // 如果有图片，设置文件列表
      if (formData.value.img) {
        fileList.value = [
          {
            name: 'image',
            url: formData.value.img
          }
        ]
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as PersonalCenterVO
    if (formType.value === 'create') {
      await PersonalCenterApi.createPersonalCenter(data)
      message.success(t('common.createSuccess'))
    } else {
      await PersonalCenterApi.updatePersonalCenter(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

// 上传处理函数
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    message.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    message.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const handleUploadSuccess: UploadProps['onSuccess'] = (response) => {
  if (response.code === 0) {
    formData.value.img = response.data
    message.success('图片上传成功')
  } else {
    message.error(response.msg || '图片上传失败')
  }
}

const handleUploadError: UploadProps['onError'] = () => {
  message.error('图片上传失败')
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    taskTypeCn: undefined,
    img: undefined,
    taskType: undefined,
    isOpen: undefined,
    jumpPath: undefined,
    isTop: undefined,
    text: undefined,
    isNew: undefined,
    businessText: undefined,
    points: undefined
  }
  fileList.value = []
  formRef.value?.resetFields()
}
</script>
