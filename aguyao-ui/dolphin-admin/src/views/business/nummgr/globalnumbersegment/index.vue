<template>
  <ContentWrap>
    <el-card>
      <template #header>
        <div class="card-header">
          <span>全球号码生成说明</span>
        </div>
      </template>
      
      <!-- 说明信息 -->
      <div style="background-color: #e6f7ff; padding: 10px; border-radius: 4px;">
        <el-alert
          title="提示: 所有号段均来自于网络，真实性有待参考，建议少量尝试，避免无效过高!"
          type="warning"
          :closable="false"
          show-icon
          class="mb-4"
        />
        
        <div class="mb-4" >
          <p class="text-sm text-gray-600 mb-2">1. 单次生成最多100w</p>
          <p class="text-sm text-gray-600 mb-2">2. 只可选择任意国家任意号码段随机生成，不可填入号码</p>
          <p class="text-sm text-gray-600 mb-2">3. 生成结果只保留最近60天的，请及时下载保存</p>
        </div>
      </div>

      <!-- 表单 -->
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        class="mt-6"
      >
        <el-row :gutter="20" style="margin-bottom: 40px;">
          <el-col :span="6">
              <el-select
                v-model="formData.country"
                placeholder="请选择国家"
                style="width: 100%"
                @change="handleCountryChange"
              >
                <el-option
                  v-for="item in countryOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            <!-- </el-form-item> -->
          </el-col>
          
          <el-col :span="6">
            <!-- <el-form-item label="" prop="operator"> -->
              <el-select
                v-model="formData.city"
                placeholder="请选择城市"
                style="width: 100%"
                @change="handleCityChange"
              >
                <el-option
                  v-for="item in cityOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            <!-- </el-form-item> -->
          </el-col>
        <!-- </el-row> -->

        <!-- <el-row :gutter="20"> -->
          <el-col :span="6">
            <!-- <el-form-item label="请选择地区号码" prop="areaCode"> -->
              <el-select
                v-model="formData.areaCode"
                placeholder="请选择地区号码"
                style="width: 100%"
                @change="handleAreaCodeChange"
                multiple
              >
                <el-option
                  v-for="item in areaCodeOptions"
                  :key="item.id"
                  :label="item.numSegment"
                  :value="item.id"
                />
              </el-select>
            <!-- </el-form-item> -->
          </el-col>
          
          <el-col :span="6">
            <!-- <el-form-item label="请选择号码" prop="numberSegment"> -->
              <el-select
                v-model="formData.numberSegment"
                placeholder="请选择号码"
                style="width: 100%"
                @change="handleNumberSegmentChange"
              >
                <el-option
                  v-for="item in numberOptions"
                  :key="item.id"
                  :label="item.number + ' - ' + item.snFrom"
                  :value="item.id"
                />
              </el-select>
            <!-- </el-form-item> -->
          </el-col>
        </el-row>



        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始号码" prop="startNumber">
              <el-input
                v-model="formData.startNumber"
                placeholder="开始号码"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="结束号码" prop="endNumber">
              <el-input
                v-model="formData.endNumber"
                placeholder=""
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="地区" prop="region">
              <el-input
                v-model="formData.region"
                placeholder=""
                disabled
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="城市" prop="city">
              <el-input
                v-model="formData.city"
                placeholder=""
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="运营商" prop="operatorName">
              <el-input
                v-model="formData.operatorName"
                placeholder=""
                disabled
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="已使用号码" prop="usedNumbers">
              <el-input
                v-model="formData.usedNumbers"
                placeholder=""
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="任务名称" prop="taskName">
              <el-input
                v-model="formData.taskName"
                placeholder="请输入任务名称"
                clearable
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="生成数量" prop="generateCount">
              <el-input-number
                v-model="formData.generateCount"
                :min="1"
                :max="1000000"
                :step="1"
                placeholder="请输入生成数量"
                style="width: 100%"
                controls-position="right"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item style="align-items: center; display: flex;">
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            提交
          </el-button>
          <el-button @click="handleReset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">

import { onMounted, ref, reactive } from 'vue'

import { CountryApi } from '@/api/business/country'
import { CityApi } from '@/api/business/city'
import { NumSegmentApi, NumSegmentVO } from '@/api/business/numsegment'
import { NumberApi, NumberVO } from '@/api/business/number'

/** 全球号码段生成 */
defineOptions({ name: 'GlobalNumberSegment' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

// 表单引用
const formRef = ref()

// 表单数据
const formData = reactive({
  country: '',
  areaCode: '',
  numberSegment: '',
  startNumber: '',
  endNumber: '',
  region: '',
  city: '',
  operatorName: '',
  usedNumbers: '',
  taskName: '',
  generateCount: 1
})

// 表单验证规则
const formRules = reactive({
  country: [{ required: true, message: '请选择国家', trigger: 'change' }],
  operator: [{ required: true, message: '请选择运营商', trigger: 'change' }],
  areaCode: [{ required: true, message: '请选择地区号码', trigger: 'change' }],
  numberSegment: [{ required: true, message: '请选择号码', trigger: 'change' }],
  taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  generateCount: [
    { required: true, message: '请输入生成数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 1000000, message: '生成数量必须在1-1000000之间', trigger: 'blur' }
  ]
})

// 提交加载状态
const submitLoading = ref(false)

interface Item {
  label: string
  value: number
}

// 国家数据
const countryOptions = ref<Item[]>([])
// 城市数据
const cityOptions = ref<Item[]>([])

const areaCodeOptions = ref<NumSegmentVO[]>([])

const numberOptions = ref<NumberVO[]>([])

/** 1、国家选择变化 */
const handleCountryChange = (value: number) => {
  // 根据国家重新加载运营商选项
  console.log('选择的国家:', value)
  // 清空下级选项
  formData.city = ''
  formData.areaCode = ''
  formData.numberSegment = ''
  clearNumberInfo()
  // 加载城市列表
  loadCityList(value)
}

// 根据国家id加载城市列表
const loadCityList = async (countryId: number) => {
  try {
    if (!countryId) {
      return
    }
    const res = await CityApi.getCityList(countryId)
    cityOptions.value = res.map(item => ({
      label: item.name,
      value: item.id
    }))
  } catch (error) {
    message.error('加载城市列表失败')
  }
}

/** 2、城市选择变化 */
const handleCityChange = (value: number) => {
  // 根据运营商重新加载地区号码选项
  console.log('选择的运营商:', value)
  // 清空下级选项
  formData.areaCode = ''
  formData.numberSegment = ''
  // clearNumberInfo()
  // 加载地区号码列表
  loadAreaCodeList(value)
}

// 根据城市id加载地区号码列表
const loadAreaCodeList = async (cityId: number) => {
  try {
    debugger
    if (!cityId) {
      return ;
    }
    const res = await NumSegmentApi.getNumSegmentList(cityId)
    areaCodeOptions.value = res
    // areaCodeOptions.value = res.map(item => ({
    //   label: item.numSegment,
    //   value: item.id
    // }))

    // 默认值 todo 
    if (res) {
      const areaCode = res[0].areaCode
      const numSegment = res[0].numSegment
      const snFrom = res[0].snFrom
      const cityName = res[0].cityName

      formData.startNumber = areaCode+numSegment+snFrom
      formData.city = cityName
      formData.usedNumbers = '全选'
    }
  } catch (error) {
    message.error('加载地区号码列表失败')
  }
}

/** 3、地区号码选择变化 */
const handleAreaCodeChange = (value: string) => {
  // 根据地区号码重新加载号码段选项
  console.log('选择的地区号码:', value)
  // 清空下级选项
  formData.numberSegment = ''
  
  loadNumberList(value)

  // debugger
  // const firstId = String(value).split(',')[0]
  const firstId = value[0]
  const it = areaCodeOptions.value.find(item => item.id === Number(firstId))
  if (it) {
      const areaCode = it.areaCode
      const numSegment = it.numSegment
      const snFrom = it.snFrom

      formData.startNumber = areaCode+numSegment+snFrom   
  }

  if (value === undefined || value.length === 0) {
    formData.usedNumbers = '全选'
    formData.region = ''
    formData.operatorName = ''
    formData.endNumber = ''
  } else {
    formData.usedNumbers = String(value)
  }
  
}

// 根据地区号码加载号码段列表
const loadNumberList = async (areaCodeId: string) => {
  try {
    if (!areaCodeId) {
      return
    }
    const res = await NumberApi.getNumberList(areaCodeId)
    numberOptions.value = res
    // numberOptions.value = res.map(item => ({
    //   label: item.number,
    //   value: item.id
    // }))
  } catch (error) {
    message.error('加载号码段列表失败')
  }
}

/** 4、号码段选择变化 */
const handleNumberSegmentChange = (value: number) => {
  // 根据选择的号码段设置相关信息
  console.log('选择的号码段:', value)
  const it = numberOptions.value.find(item => item.id === value)
  if (it) {
    // formData.startNumber = it.number
    debugger
      const areaCode = it.areaCode
      const numSegment = it.segmentName
      const snTo = it.snTo

    formData.endNumber = areaCode+numSegment+snTo 
    formData.region = it.area
    formData.operatorName = it.operator
  }
}

/** 清空号码信息 */
const clearNumberInfo = () => {
  formData.startNumber = ''
  formData.endNumber = ''
  formData.region = ''
  formData.city = ''
  formData.operatorName = ''
  formData.usedNumbers = ''
  formData.numberSegment = ''
  formData.areaCode = ''
}

/** 提交表单 */
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate()
    
    submitLoading.value = true
    
    // 模拟API调用
    console.log('提交的表单数据:', formData)
    
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    message.success('号码生成任务提交成功！')
    
    // 重置表单
    handleReset()
    
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitLoading.value = false
  }
}

/** 重置表单 */
const handleReset = () => {
  formRef.value?.resetFields()
  clearNumberInfo()
  formData.generateCount = 1
  message.info('表单已重置')
}

// 监听号码段选择变化
// watch(() => formData.numberSegment, (newValue) => {
//   if (newValue) {
//     handleNumberSegmentChange(newValue)
//   }
// })


onMounted( async () => {
  // 加载国家数据
  const countryRes = await CountryApi.getCountryList()
  countryOptions.value = countryRes.map(item => ({
    label: item.code + ' - ' + item.name,
    value: item.id
  }))
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}
</style>
