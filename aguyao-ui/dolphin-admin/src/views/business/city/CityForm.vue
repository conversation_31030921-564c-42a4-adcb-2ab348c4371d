<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <!-- <el-form-item label="国家" prop="countryName">
        <el-input v-model="formData.countryName" placeholder="请输入国家名称" />
      </el-form-item> -->
      <el-form-item label="国家" prop="countryName">
          <el-input 
            v-model="formData.countryName" 
            placeholder="请选择国家" 
            readonly
            style="width: 100%;"
          >
            <template #append>
              <el-button @click="openCountrySelect" type="primary">
                <Icon icon="ep:search" /> 选择
              </el-button>
            </template>
          </el-input>
        </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入城市名称" />
      </el-form-item>
      <el-form-item label="描述" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入描述" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>

      <!-- 国家选择弹窗 -->
  <CountrySelect 
    v-model="countrySelectVisible" 
    @confirm="handleCountrySelect" 
  />
</template>
<script setup lang="ts">
import { CountryVO } from '@/api/business/country'
import { CityApi, CityVO } from '@/api/business/city'
import CountrySelect from '@/views/business/country/CountrySelect.vue'

/** 城市 表单 */
defineOptions({ name: 'CityForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  countryId: undefined as number | undefined,
  countryName: undefined as string | undefined,
  name: undefined,
  remark: undefined
})
const formRules = reactive({
  countryName: [
    { required: true, message: '请输入国家名称', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' }
  ],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CityApi.getCity(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CityVO
    if (formType.value === 'create') {
      await CityApi.createCity(data)
      message.success(t('common.createSuccess'))
    } else {
      await CityApi.updateCity(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    countryId: undefined,
    countryName: undefined,
    name: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}


// 国家选择弹窗显示状态
const countrySelectVisible = ref(false)

/** 打开国家选择弹窗 */
const openCountrySelect = () => {
  countrySelectVisible.value = true
}


/** 处理国家选择确认 */
const handleCountrySelect = (country: CountryVO) => {
  formData.value.countryId = country.id
  formData.value.countryName = country.name
  message.success(`已选择国家：${country.name}`)
  console.log(`已选择国家：${country.name}`)
}
</script>
