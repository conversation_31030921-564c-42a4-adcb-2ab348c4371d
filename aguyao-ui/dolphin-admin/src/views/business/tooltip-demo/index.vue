<template>
  <ContentWrap title="InfoTooltip 组件示例">
    <div class="tooltip-examples">
      <!-- 基础用法 -->
      <div class="example-section">
        <h3>基础用法</h3>
        <div class="example-content">
          <p>
            这是一个普通的文本，其中包含一个
            <InfoTooltip
              trigger-text="可点击的链接"
              title="基础 Tooltip"
              content="这是一个基础的 tooltip 示例，展示简单的文本内容。"
              placement="top"
            />
            用于展示额外信息。
          </p>
        </div>
      </div>

      <!-- 不同位置 -->
      <div class="example-section">
        <h3>不同弹出位置</h3>
        <div class="example-content placement-demo">
          <div class="placement-row">
            <InfoTooltip
              trigger-text="上方弹出"
              content="这个 tooltip 在上方弹出"
              placement="top"
            />
            <InfoTooltip
              trigger-text="下方弹出"
              content="这个 tooltip 在下方弹出"
              placement="bottom"
            />
          </div>
          <div class="placement-row">
            <InfoTooltip
              trigger-text="左侧弹出"
              content="这个 tooltip 在左侧弹出"
              placement="left"
            />
            <InfoTooltip
              trigger-text="右侧弹出"
              content="这个 tooltip 在右侧弹出"
              placement="right"
            />
          </div>
        </div>
      </div>

      <!-- 富文本内容 -->
      <div class="example-section">
        <h3>富文本内容</h3>
        <div class="example-content">
          <p>
            查看
            <InfoTooltip
              trigger-text="详细说明"
              title="产品介绍"
              :content="richContent"
              placement="bottom"
              :max-width="400"
            />
            了解更多信息。
          </p>
        </div>
      </div>

      <!-- 自定义触发器 -->
      <div class="example-section">
        <h3>自定义触发器</h3>
        <div class="example-content">
          <InfoTooltip
            title="自定义按钮触发"
            content="你可以使用任何元素作为触发器，比如按钮、图标等。"
            placement="top"
          >
            <template #trigger>
              <el-button type="primary" size="small">
                <Icon icon="ep:question-filled" />
                悬浮查看帮助
              </el-button>
            </template>
          </InfoTooltip>

          <InfoTooltip
            title="图标触发"
            content="这是一个图标触发的示例"
            placement="top"
            class="ml-4"
          >
            <template #trigger>
              <Icon 
                icon="ep:info-filled" 
                class="info-icon" 
                :size="20"
              />
            </template>
          </InfoTooltip>
        </div>
      </div>

      <!-- 长内容 -->
      <div class="example-section">
        <h3>长内容展示</h3>
        <div class="example-content">
          <InfoTooltip
            trigger-text="查看长文本内容"
            title="长文本示例"
            :content="longContent"
            placement="bottom"
            :max-width="500"
          />
        </div>
      </div>

      <!-- 延迟设置 -->
      <div class="example-section">
        <h3>延迟设置</h3>
        <div class="example-content">
          <InfoTooltip
            trigger-text="无延迟"
            content="立即显示的 tooltip"
            :show-delay="0"
            :hide-delay="0"
            placement="top"
          />
          <InfoTooltip
            trigger-text="延迟显示"
            content="延迟 500ms 显示的 tooltip"
            :show-delay="500"
            :hide-delay="200"
            placement="top"
            class="ml-4"
          />
        </div>
      </div>

      <!-- 禁用状态 -->
      <div class="example-section">
        <h3>禁用状态</h3>
        <div class="example-content">
          <InfoTooltip
            trigger-text="正常状态"
            content="这是正常状态的 tooltip"
            placement="top"
          />
          <InfoTooltip
            trigger-text="禁用状态"
            content="这个 tooltip 被禁用了"
            placement="top"
            :disabled="true"
            class="ml-4"
          />
        </div>
      </div>
    </div>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 富文本内容
const richContent = ref(`
  <div style="line-height: 1.6;">
    <p><strong>产品特性：</strong></p>
    <ul style="margin: 8px 0; padding-left: 20px;">
      <li>高性能架构设计</li>
      <li>响应式布局适配</li>
      <li>多语言国际化支持</li>
      <li>丰富的组件库</li>
    </ul>
    <p style="margin-top: 12px; color: #409eff;">
      <strong>💡 提示：</strong>点击卡片可查看详细信息
    </p>
  </div>
`)

// 长文本内容
const longContent = ref(`
  <div style="line-height: 1.8;">
    <p><strong>详细说明文档</strong></p>
    <p>这是一个包含大量文本信息的 tooltip 示例。在实际应用中，你可能需要展示：</p>
    
    <h4 style="margin: 12px 0 6px; color: #303133;">功能介绍</h4>
    <p>InfoTooltip 组件提供了丰富的配置选项，包括自定义触发器、多种弹出位置、延迟设置、禁用状态等功能。</p>
    
    <h4 style="margin: 12px 0 6px; color: #303133;">使用场景</h4>
    <ul style="margin: 6px 0; padding-left: 18px;">
      <li>帮助说明和提示信息</li>
      <li>术语解释和名词说明</li>
      <li>操作指导和用户引导</li>
      <li>状态说明和详细信息</li>
    </ul>
    
    <div style="margin-top: 16px; padding: 8px; background: #f0f9ff; border-radius: 4px; border-left: 3px solid #409eff;">
      <strong>注意：</strong>当内容过长时，建议设置合适的最大宽度以保证良好的阅读体验。
    </div>
  </div>
`)
</script>

<style scoped>
.tooltip-examples {
  max-width: 1000px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 40px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.example-section h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.example-content {
  font-size: 14px;
  line-height: 1.6;
  color: #606266;
}

.placement-demo {
  text-align: center;
}

.placement-row {
  margin: 16px 0;
  display: flex;
  justify-content: center;
  gap: 40px;
}

.info-icon {
  color: #409eff;
  cursor: pointer;
  transition: color 0.3s;
}

.info-icon:hover {
  color: #337ecc;
}

.ml-4 {
  margin-left: 16px;
}

/* 为示例页面添加一些额外的样式 */
:deep(.info-tooltip-trigger .trigger-text) {
  color: #409eff;
  text-decoration: underline;
  text-decoration-style: dotted;
  cursor: pointer;
  font-weight: 500;
}
</style>