<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <!-- <el-form-item label="城市" prop="cityName">
        <el-input v-model="formData.cityName" placeholder="请输入城市名称" />
      </el-form-item> -->
      <el-form-item label="城市">
          <el-input 
            v-model="selectedCityDisplay" 
            placeholder="请选择城市" 
            readonly
            style="width: 100%"
          >
            <template #append>
              <el-button @click="openCitySelect" type="primary">
                <Icon icon="ep:search" /> 选择
              </el-button>
            </template>
          </el-input>
        </el-form-item>
      <el-form-item label="号段" prop="numSegment">
        <el-input v-model="formData.numSegment" placeholder="请输入地区号段" />
      </el-form-item>
      <el-form-item label="描述" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入描述" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>

    <!-- 城市选择弹窗 -->
    <CitySelect 
      v-model="citySelectVisible" 
      @confirm="handleCitySelect" 
    />
</template>
<script setup lang="ts">
import CitySelect from '@/views/business/city/CitySelect.vue'
import { CityApi, CityVO } from '@/api/business/city'
import { NumSegmentApi, NumSegmentVO } from '@/api/business/numsegment'

/** 地区号段 表单 */
defineOptions({ name: 'NumSegmentForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  cityId: undefined as number | undefined,
  cityName: undefined as string | undefined,
  numSegment: undefined,
  remark: undefined
})
const formRules = reactive({
  cityName: [
    { required: true, message: '请输入城市名称', trigger: 'blur' }
  ],
  numSegment: [
    { required: true, message: '请输入号段', trigger: 'blur' }
  ],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await NumSegmentApi.getNumSegment(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as NumSegmentVO
    if (formType.value === 'create') {
      await NumSegmentApi.createNumSegment(data)
      message.success(t('common.createSuccess'))
    } else {
      await NumSegmentApi.updateNumSegment(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    cityId: undefined,
    cityName: undefined,
    numSegment: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}


// 城市选择弹窗显示状态
const citySelectVisible = ref(false)

// 选中的城市显示文本
const selectedCityDisplay = computed(() => {
  if (formData.value.cityName) {
    return `${formData.value.cityName}`
  }
  return ''
})

/** 打开城市选择弹窗 */
const openCitySelect = () => {
  citySelectVisible.value = true
}

/** 处理城市选择确认 */
const handleCitySelect = (city: CityVO) => {
  formData.value.cityId = city.id
  formData.value.cityName = city.name
  message.success(`已选择城市：${city.name}`)
}
</script>
