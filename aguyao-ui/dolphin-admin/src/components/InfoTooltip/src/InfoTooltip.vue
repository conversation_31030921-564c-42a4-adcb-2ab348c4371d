<template>
  <div
    ref="triggerRef"
    class="info-tooltip-trigger"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- 触发区域内容 -->
    <slot name="trigger">
      <span class="trigger-text">{{ triggerText }}</span>
    </slot>

    <!-- Tooltip 内容 -->
    <Teleport to="body">
      <Transition name="tooltip-fade">
        <div
          v-if="visible"
          ref="tooltipRef"
          class="info-tooltip-content"
          :style="tooltipStyle"
          @mouseenter="handleTooltipMouseEnter"
          @mouseleave="handleTooltipMouseLeave"
        >
          <div class="tooltip-header" v-if="title">
            <h4 class="tooltip-title">{{ title }}</h4>
          </div>
          <div class="tooltip-body">
            <slot name="content">
              <div v-html="content"></div>
            </slot>
          </div>
          <!-- 箭头指示器 -->
          <div class="tooltip-arrow" :class="`arrow-${placement}`"></div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue'

interface Props {
  /** 触发文本 */
  triggerText?: string
  /** 标题 */
  title?: string
  /** 内容 */
  content?: string
  /** 显示延迟（毫秒） */
  showDelay?: number
  /** 隐藏延迟（毫秒） */
  hideDelay?: number
  /** 弹出位置 */
  placement?: 'top' | 'bottom' | 'left' | 'right'
  /** 最大宽度 */
  maxWidth?: string | number
  /** 是否禁用 */
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  triggerText: '',
  title: '',
  content: '',
  showDelay: 100,
  hideDelay: 100,
  placement: 'top',
  maxWidth: '300px',
  disabled: false
})

const emit = defineEmits<{
  show: []
  hide: []
}>()

// 响应式数据
const visible = ref(false)
const triggerRef = ref<HTMLElement>()
const tooltipRef = ref<HTMLElement>()
const showTimer = ref<number>()
const hideTimer = ref<number>()

// 计算 tooltip 位置
const tooltipStyle = ref({
  left: '0px',
  top: '0px',
  maxWidth: typeof props.maxWidth === 'number' ? `${props.maxWidth}px` : props.maxWidth
})

// 清除定时器
const clearTimers = () => {
  if (showTimer.value) {
    clearTimeout(showTimer.value)
    showTimer.value = undefined
  }
  if (hideTimer.value) {
    clearTimeout(hideTimer.value)
    hideTimer.value = undefined
  }
}

// 计算弹窗位置
const calculatePosition = async () => {
  await nextTick()
  
  if (!triggerRef.value || !tooltipRef.value) return

  const triggerRect = triggerRef.value.getBoundingClientRect()
  const tooltipRect = tooltipRef.value.getBoundingClientRect()
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight
  
  let left = 0
  let top = 0
  const offset = 8 // 箭头偏移量

  switch (props.placement) {
    case 'top':
      left = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2
      top = triggerRect.top - tooltipRect.height - offset
      break
    case 'bottom':
      left = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2
      top = triggerRect.bottom + offset
      break
    case 'left':
      left = triggerRect.left - tooltipRect.width - offset
      top = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2
      break
    case 'right':
      left = triggerRect.right + offset
      top = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2
      break
  }

  // 防止超出视口边界
  if (left < 10) left = 10
  if (left + tooltipRect.width > viewportWidth - 10) {
    left = viewportWidth - tooltipRect.width - 10
  }
  if (top < 10) top = 10
  if (top + tooltipRect.height > viewportHeight - 10) {
    top = viewportHeight - tooltipRect.height - 10
  }

  tooltipStyle.value = {
    ...tooltipStyle.value,
    left: `${left}px`,
    top: `${top}px`
  }
}

// 显示 tooltip
const showTooltip = () => {
  if (props.disabled) return
  
  clearTimers()
  showTimer.value = window.setTimeout(async () => {
    visible.value = true
    await calculatePosition()
    emit('show')
  }, props.showDelay)
}

// 隐藏 tooltip
const hideTooltip = () => {
  clearTimers()
  hideTimer.value = window.setTimeout(() => {
    visible.value = false
    emit('hide')
  }, props.hideDelay)
}

// 事件处理
const handleMouseEnter = () => {
  showTooltip()
}

const handleMouseLeave = () => {
  hideTooltip()
}

const handleTooltipMouseEnter = () => {
  clearTimers()
}

const handleTooltipMouseLeave = () => {
  hideTooltip()
}

// 监听窗口大小变化，重新计算位置
watch(visible, (newVisible) => {
  if (newVisible) {
    const handleResize = () => calculatePosition()
    window.addEventListener('resize', handleResize)
    window.addEventListener('scroll', handleResize)
    
    // 组件销毁时移除监听
    return () => {
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('scroll', handleResize)
    }
  }
})

// 暴露方法
defineExpose({
  show: showTooltip,
  hide: hideTooltip
})
</script>

<style scoped>
.info-tooltip-trigger {
  display: inline-block;
  cursor: pointer;
}

.trigger-text {
  color: #409eff;
  text-decoration: underline;
  text-decoration-style: dotted;
  font-size: inherit;
}

.trigger-text:hover {
  color: #337ecc;
}

.info-tooltip-content {
  position: fixed;
  z-index: 9999;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 0;
  font-size: 14px;
  line-height: 1.5;
  color: #606266;
  word-wrap: break-word;
  min-width: 240px;
}

.tooltip-header {
  padding: 12px 16px 8px;
  border-bottom: 1px solid #f0f0f0;
}

.tooltip-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.tooltip-body {
  padding: 12px 16px;
}

.tooltip-arrow {
  position: absolute;
  width: 8px;
  height: 8px;
  background: white;
  border: 1px solid #e4e7ed;
  transform: rotate(45deg);
}

.arrow-top {
  bottom: -5px;
  left: 50%;
  margin-left: -4px;
  border-top: none;
  border-left: none;
}

.arrow-bottom {
  top: -5px;
  left: 50%;
  margin-left: -4px;
  border-bottom: none;
  border-right: none;
}

.arrow-left {
  right: -5px;
  top: 50%;
  margin-top: -4px;
  border-left: none;
  border-bottom: none;
}

.arrow-right {
  left: -5px;
  top: 50%;
  margin-top: -4px;
  border-right: none;
  border-top: none;
}

/* 过渡动画 */
.tooltip-fade-enter-active,
.tooltip-fade-leave-active {
  transition: all 0.2s ease;
}

.tooltip-fade-enter-from,
.tooltip-fade-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

.tooltip-fade-enter-to,
.tooltip-fade-leave-from {
  opacity: 1;
  transform: scale(1);
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .info-tooltip-content {
    background: #2d2d2d;
    border-color: #4c4d4f;
    color: #e4e7ed;
  }
  
  .tooltip-title {
    color: #ffffff;
  }
  
  .tooltip-header {
    border-bottom-color: #4c4d4f;
  }
  
  .tooltip-arrow {
    background: #2d2d2d;
    border-color: #4c4d4f;
  }
}
</style>