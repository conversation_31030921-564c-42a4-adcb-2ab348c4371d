export interface InfoTooltipProps {
  /** 触发文本 */
  triggerText?: string
  /** 标题 */
  title?: string
  /** 内容 */
  content?: string
  /** 显示延迟（毫秒） */
  showDelay?: number
  /** 隐藏延迟（毫秒） */
  hideDelay?: number
  /** 弹出位置 */
  placement?: 'top' | 'bottom' | 'left' | 'right'
  /** 最大宽度 */
  maxWidth?: string | number
  /** 是否禁用 */
  disabled?: boolean
}

export interface InfoTooltipEmits {
  show: []
  hide: []
}

export interface InfoTooltipInstance {
  show: () => void
  hide: () => void
}