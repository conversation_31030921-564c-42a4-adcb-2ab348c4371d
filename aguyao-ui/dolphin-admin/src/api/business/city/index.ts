import request from '@/config/axios'

// 城市 VO
export interface CityVO {
  id: number // 主键
  countryId: number // 国家id
  countryName: string // 国家名称
  name: string // 城市名称
  remark: string // 描述
}

// 城市 API
export const CityApi = {
  // 查询城市分页
  getCityPage: async (params: any) => {
    return await request.get({ url: `/busi/city/page`, params })
  },

  // 查询城市详情
  getCity: async (id: number) => {
    return await request.get({ url: `/busi/city/get?id=` + id })
  },

  // 新增城市
  createCity: async (data: CityVO) => {
    return await request.post({ url: `/busi/city/create`, data })
  },

  // 修改城市
  updateCity: async (data: CityVO) => {
    return await request.put({ url: `/busi/city/update`, data })
  },

  // 删除城市
  deleteCity: async (id: number) => {
    return await request.delete({ url: `/busi/city/delete?id=` + id })
  },

  // 导出城市 Excel
  exportCity: async (params) => {
    return await request.download({ url: `/busi/city/export-excel`, params })
  },
}
