import request from '@/config/axios'

// 号码 VO
export interface NumberVO {
  id: number // 主键
  segmentId: number // 号段id
  segmentName: string // 号段名称
  number: string // 号码，存在000的情况
  snFrom: string // 序列号起始值
  operator: string // 运营商
  snTo: string // 序列号结束值
  snNumber: string // 序列号
  area: string  // 区域
  areaCode: string // 区号
  digit: number // 位数
  remark: string // 描述
}

// 号码 API
export const NumberApi = {
  // 查询号码分页
  getNumberPage: async (params: any) => {
    return await request.get({ url: `/busi/number/page`, params })
  },

  // 查询号码详情
  getNumber: async (id: number) => {
    return await request.get({ url: `/busi/number/get?id=` + id })
  },

  // 新增号码
  createNumber: async (data: NumberVO) => {
    return await request.post({ url: `/busi/number/create`, data })
  },

  // 修改号码
  updateNumber: async (data: NumberVO) => {
    return await request.put({ url: `/busi/number/update`, data })
  },

  // 删除号码
  deleteNumber: async (id: number) => {
    return await request.delete({ url: `/busi/number/delete?id=` + id })
  },

  // 导出号码 Excel
  exportNumber: async (params) => {
    return await request.download({ url: `/busi/number/export-excel`, params })
  },

    // 查询号码列表
  getNumberList: async (segmentIds: string) => {
    return await request.get({ url: `/busi/number/get-all-list?segmentIds=${segmentIds}` })
  },
}
