import request from '@/config/axios'

// 地区号段 VO
export interface NumSegmentVO {
  id: number // 主键
  cityId: number // 城市id
  cityName: string // 城市名称
  numSegment: string // 地区号段
  snFrom: string // 序列号起始值
  remark: string // 描述
  area: string // 地区
  areaCode: string  // 地区编码
}

// 地区号段 API
export const NumSegmentApi = {
  // 查询地区号段分页
  getNumSegmentPage: async (params: any) => {
    return await request.get({ url: `/busi/num-segment/page`, params })
  },

  // 查询地区号段详情
  getNumSegment: async (id: number) => {
    return await request.get({ url: `/busi/num-segment/get?id=` + id })
  },

  // 新增地区号段
  createNumSegment: async (data: NumSegmentVO) => {
    return await request.post({ url: `/busi/num-segment/create`, data })
  },

  // 修改地区号段
  updateNumSegment: async (data: NumSegmentVO) => {
    return await request.put({ url: `/busi/num-segment/update`, data })
  },

  // 删除地区号段
  deleteNumSegment: async (id: number) => {
    return await request.delete({ url: `/busi/num-segment/delete?id=` + id })
  },

  // 导出地区号段 Excel
  exportNumSegment: async (params) => {
    return await request.download({ url: `/busi/num-segment/export-excel`, params })
  },

    // 查询城市列表
  getNumSegmentList: async (cityId: number) => {
    return await request.get({ url: `/busi/num-segment/get-all-list?cityId=${cityId}` })
  },
}
