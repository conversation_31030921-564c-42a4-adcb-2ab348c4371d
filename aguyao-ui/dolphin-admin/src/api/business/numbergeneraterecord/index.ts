import request from '@/config/axios'

// 号码生成任务记录 VO
export interface NumberGenerateRecordVO {
  id: number // 主键
  taskType: number // 任务类型，1：全球号码生成；2：自定义号码生成
  totalNum: number // 数据总量
  generalNum: number // 生成数量
  countryCode: string // 国家简称，如：AD
  countryName: string // 国家名称
  cityCode: string // 城市编码
  cityName: string // 城市名称
  areaCode: string // 区号
  areaName: string // 区域名称
  numSegment: string // 号段
  selectNum: string // 选择号码
  startNumber: string // 起始号码
  endNumber: string // 结束号码
  operator: string // 运营商
  remark: string // 描述
}

// 号码生成任务记录 API
export const NumberGenerateRecordApi = {
  // 查询号码生成任务记录分页
  getNumberGenerateRecordPage: async (params: any) => {
    return await request.get({ url: `/busi/number-generate-record/page`, params })
  },

  // 查询号码生成任务记录详情
  getNumberGenerateRecord: async (id: number) => {
    return await request.get({ url: `/busi/number-generate-record/get?id=` + id })
  },

  // 新增号码生成任务记录
  createNumberGenerateRecord: async (data: NumberGenerateRecordVO) => {
    return await request.post({ url: `/busi/number-generate-record/create`, data })
  },

  // 修改号码生成任务记录
  updateNumberGenerateRecord: async (data: NumberGenerateRecordVO) => {
    return await request.put({ url: `/busi/number-generate-record/update`, data })
  },

  // 删除号码生成任务记录
  deleteNumberGenerateRecord: async (id: number) => {
    return await request.delete({ url: `/busi/number-generate-record/delete?id=` + id })
  },

  // 导出号码生成任务记录 Excel
  exportNumberGenerateRecord: async (params) => {
    return await request.download({ url: `/busi/number-generate-record/export-excel`, params })
  },

  // 导出号码生成任务记录数据
  exportData: async (params) => {
    return await request.download({ url: `/busi/number-generate-record/export-data`, params })
  },
}
