import { Layout } from '@/utils/routerHelper'
import { t } from '@/hooks/web/useI18n'

const { VITE_HIDE_HOME } = import.meta.env
const isHide = VITE_HIDE_HOME === 'true'

const dolphinRouter = {
  path: '/dolphin',
  component: Layout,
  redirect: '/dolphin/task',
  name: 'Dolphin',
  meta: {
    title: t('router.dolphin'),
    icon: 'ep:data-analysis',
    alwaysShow: true
  },
  children: [
    // {
    //   path: 'task',
    //   component: () => import('@/views/dolphin/common/GenericListPage.vue'),
    //   name: 'DolphinTask',
    //   meta: {
    //     title: '任务管理',
    //     icon: 'ep:list',
    //     pageKey: 'task', // 指定使用的页面配置key
    //     noCache: false
    //   }
    // },
    {
      path: 'project',
      component: () => import('@/views/dolphin/common/GenericListPage.vue'),
      name: 'DolphinProject',
      meta: {
        title: '项目管理',
        icon: 'ep:folder',
        pageKey: 'project', // 指定使用的页面配置key
        noCache: false
      }
    },
    {
      path: 'user',
      component: () => import('@/views/dolphin/common/GenericListPage.vue'),
      name: 'DolphinUser',
      meta: {
        title: '用户管理',
        icon: 'ep:user',
        pageKey: 'user', // 指定使用的页面配置key
        noCache: false
      }
    },
    {
      path: 'department',
      component: () => import('@/views/dolphin/common/GenericListPage.vue'),
      name: 'DolphinDepartment',
      meta: {
        title: '部门管理',
        icon: 'ep:office-building',
        pageKey: 'department', // 指定使用的页面配置key
        noCache: false
      }
    },
    {
      path: 'role',
      component: () => import('@/views/dolphin/common/GenericListPage.vue'),
      name: 'DolphinRole',
      meta: {
        title: '角色管理',
        icon: 'ep:avatar',
        pageKey: 'role', // 指定使用的页面配置key
        noCache: false
      }
    },
    {
      path: 'permission',
      component: () => import('@/views/dolphin/common/GenericListPage.vue'),
      name: 'DolphinPermission',
      meta: {
        title: '权限管理',
        icon: 'ep:key',
        pageKey: 'permission', // 指定使用的页面配置key
        noCache: false
      }
    },
    {
      path: 'log',
      component: () => import('@/views/dolphin/common/GenericListPage.vue'),
      name: 'DolphinLog',
      meta: {
        title: '操作日志',
        icon: 'ep:document',
        pageKey: 'log', // 指定使用的页面配置key
        noCache: false
      }
    },
    {
      path: 'config',
      component: () => import('@/views/dolphin/common/GenericListPage.vue'),
      name: 'DolphinConfig',
      meta: {
        title: '系统配置',
        icon: 'ep:setting',
        pageKey: 'config', // 指定使用的页面配置key
        noCache: false
      }
    },
    {
      path: 'notice',
      component: () => import('@/views/dolphin/common/GenericListPage.vue'),
      name: 'DolphinNotice',
      meta: {
        title: '通知公告',
        icon: 'ep:bell',
        pageKey: 'notice', // 指定使用的页面配置key
        noCache: false
      }
    },
    {
      path: 'file',
      component: () => import('@/views/dolphin/common/GenericListPage.vue'),
      name: 'DolphinFile',
      meta: {
        title: '文件管理',
        icon: 'ep:folder-opened',
        pageKey: 'file', // 指定使用的页面配置key
        noCache: false
      }
    },
    // 动态路由测试页面
    {
      path: 'dynamic-test',
      component: () => import('@/views/dolphin/test/DynamicRouteTest.vue'),
      name: 'DolphinDynamicTest',
      meta: {
        title: '动态路由测试',
        icon: 'ep:cpu',
        noCache: false
        // 故意不设置 pageKey，测试动态推断
      }
    },
    // 测试通用页面的动态路由
    {
      path: 'task',
      component: () => import('@/views/dolphin/common/GenericListPage.vue'),
      name: 'DolphinTaskDynamic',
      meta: {
        title: '任务管理(动态)',
        icon: 'ep:list',
        noCache: false
        // 故意不设置 pageKey，测试从路径推断
      }
    },
    // 可以继续添加更多页面...
    // {
    //   path: 'custom-page',
    //   component: () => import('@/views/dolphin/CustomPage.vue'), // 自定义页面
    //   name: 'DolphinCustomPage',
    //   meta: {
    //     title: '自定义页面',
    //     icon: 'ep:star',
    //     noCache: false
    //   }
    // }
  ]
}

export default dolphinRouter
