<template>
  <div class="generic-search">
    <el-form
      ref="searchFormRef"
      :model="searchForm"
      :inline="true"
      label-width="auto"
      v-bind="searchProps"
    >
      <el-row :gutter="2">
        <el-col
          v-for="field in visibleFields"
          :key="field.key"
          :span="field.span || getDefaultSpan(field.component)"
        >
          <el-form-item
            :label="field.label"
            :prop="field.key"
            :rules="field.rules"
          >
            <!-- 输入框 -->
            <el-input
              v-if="field.component === 'input'"
              v-model="searchForm[field.key]"
              :placeholder="field.placeholder || `请输入${field.label}`"
              clearable
              v-bind="field.props"
            />
            
            <!-- 数字输入框 -->
            <el-input-number
              v-else-if="field.component === 'number'"
              v-model="searchForm[field.key]"
              :placeholder="field.placeholder || `请输入${field.label}`"
              style="width: 100%"
              v-bind="field.props"
            />
            
            <!-- 下拉选择 -->
            <el-select
              v-else-if="field.component === 'select'"
              v-model="searchForm[field.key]"
              :placeholder="field.placeholder || `请选择${field.label}`"
              clearable
              style="width: 150px"
              v-bind="field.props"
            >
              <el-option
                v-for="option in field.options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
            
            <!-- 日期选择 -->
            <el-date-picker
              v-else-if="field.component === 'date'"
              v-model="searchForm[field.key]"
              type="date"
              :placeholder="field.placeholder || `请选择${field.label}`"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              clearable
              v-bind="field.props"
            />
            
            <!-- 日期范围选择 -->
            <el-date-picker
              v-else-if="field.component === 'daterange'"
              v-model="searchForm[field.key]"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              clearable
              v-bind="field.props"
            />
            
            <!-- 日期时间选择 -->
            <el-date-picker
              v-else-if="field.component === 'datetime'"
              v-model="searchForm[field.key]"
              type="datetime"
              :placeholder="field.placeholder || `请选择${field.label}`"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              clearable
              v-bind="field.props"
            />
            
            <!-- 日期时间范围选择 -->
            <el-date-picker
              v-else-if="field.component === 'datetimerange'"
              v-model="searchForm[field.key]"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              clearable
              v-bind="field.props"
            />
            
            <!-- 级联选择 -->
            <el-cascader
              v-else-if="field.component === 'cascader'"
              v-model="searchForm[field.key]"
              :options="field.options"
              :placeholder="field.placeholder || `请选择${field.label}`"
              style="width: 100%"
              clearable
              v-bind="field.props"
            />
            
            <!-- 树形选择 -->
            <el-tree-select
              v-else-if="field.component === 'tree-select'"
              v-model="searchForm[field.key]"
              :data="field.options"
              :placeholder="field.placeholder || `请选择${field.label}`"
              style="width: 100%"
              clearable
              v-bind="field.props"
            />
            
            <!-- 单选框组 -->
            <el-radio-group
              v-else-if="field.component === 'radio'"
              v-model="searchForm[field.key]"
              v-bind="field.props"
            >
              <el-radio
                v-for="option in field.options"
                :key="option.value"
                :label="option.value"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
            
            <!-- 多选框组 -->
            <el-checkbox-group
              v-else-if="field.component === 'checkbox'"
              v-model="searchForm[field.key]"
              v-bind="field.props"
            >
              <el-checkbox
                v-for="option in field.options"
                :key="option.value"
                :label="option.value"
              >
                {{ option.label }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        
        <!-- 操作按钮 -->
        <el-col :span="6">
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">
              <Icon icon="ep:search" class="mr-5px" />
              搜索
            </el-button>
            <el-button @click="handleReset">
              <Icon icon="ep:refresh" class="mr-5px" />
              重置
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import type { SearchFieldConfig } from '@/types/generic-page'
import { Icon } from '@/components/Icon'

interface Props {
  fields: SearchFieldConfig[]
  loading?: boolean
  searchProps?: Record<string, any>
}

interface Emits {
  search: [params: Record<string, any>]
  reset: []
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  searchProps: () => ({})
})

const emit = defineEmits<Emits>()

const searchFormRef = ref()
const searchForm = reactive<Record<string, any>>({})

// 计算可见字段
const visibleFields = computed(() => {
  return props.fields.filter(field => field.show !== false)
})

// 根据组件类型获取默认宽度
const getDefaultSpan = (component: string) => {
  switch (component) {
    case 'select':
    case 'cascader':
    case 'tree-select':
      return 8 // 选择类组件需要更多空间
    case 'daterange':
    case 'datetimerange':
      return 12 // 日期范围选择器需要更多空间
    case 'input':
    case 'number':
    case 'date':
    case 'datetime':
    default:
      return 6 // 默认宽度
  }
}

// 初始化表单数据
const initFormData = () => {
  visibleFields.value.forEach(field => {
    if (field.defaultValue !== undefined) {
      searchForm[field.key] = field.defaultValue
    } else {
      // 根据组件类型设置默认值
      switch (field.component) {
        case 'checkbox':
          searchForm[field.key] = []
          break
        case 'daterange':
        case 'datetimerange':
          searchForm[field.key] = []
          break
        default:
          searchForm[field.key] = undefined
      }
    }
  })
}

// 搜索
const handleSearch = async () => {
  try {
    await searchFormRef.value?.validate()
    const params = { ...searchForm }
    
    // 处理日期范围参数
    visibleFields.value.forEach(field => {
      if ((field.component === 'daterange' || field.component === 'datetimerange') && params[field.key]) {
        const [start, end] = params[field.key]
        delete params[field.key]
        params[`${field.key}Start`] = start
        params[`${field.key}End`] = end
      }
    })
    
    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
      // 过滤空数组（但保留有值的数组）
      if (Array.isArray(params[key]) && params[key].length === 0) {
        delete params[key]
      }
    })
    
    emit('search', params)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 重置
const handleReset = () => {
  searchFormRef.value?.resetFields()
  initFormData()
  emit('reset')
}

// 监听字段变化，重新初始化表单
watch(
  () => props.fields,
  () => {
    initFormData()
  },
  { deep: true, immediate: true }
)

onMounted(() => {
  initFormData()
})

// 暴露方法给父组件
defineExpose({
  search: handleSearch,
  reset: handleReset,
  validate: () => searchFormRef.value?.validate(),
  getFormData: () => ({ ...searchForm })
})
</script>

<style scoped>
.generic-search {
  background: #fff;
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.generic-search :deep(.el-form-item) {
  margin-bottom: 16px;
}

.generic-search :deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
