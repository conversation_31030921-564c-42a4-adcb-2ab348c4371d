// 搜索组件类型
export type SearchComponentType = 
  | 'input'
  | 'select'
  | 'date'
  | 'daterange'
  | 'datetime'
  | 'datetimerange'
  | 'number'
  | 'cascader'
  | 'tree-select'
  | 'radio'
  | 'checkbox'

// 搜索字段配置
export interface SearchFieldConfig {
  key: string // 字段名
  label: string // 显示标签
  component: SearchComponentType // 组件类型
  placeholder?: string // 占位符
  options?: Array<{ label: string; value: any }> // 选项数据（用于select、radio、checkbox）
  props?: Record<string, any> // 组件属性
  defaultValue?: any // 默认值
  rules?: any[] // 验证规则
  span?: number // 栅格占位格数，默认6
  show?: boolean // 是否显示，默认true
}

// 表格列配置
export interface TableColumnConfig {
  prop: string // 字段名
  label: string // 列标题
  width?: string | number // 列宽
  minWidth?: string | number // 最小列宽
  fixed?: boolean | 'left' | 'right' // 固定列
  sortable?: boolean // 是否可排序
  formatter?: (row: any, column: any, cellValue: any, index: number) => string // 格式化函数
  show?: boolean // 是否显示，默认true
  type?: 'selection' | 'index' | 'expand' // 特殊列类型
  align?: 'left' | 'center' | 'right' // 对齐方式
  headerAlign?: 'left' | 'center' | 'right' // 表头对齐方式
  showOverflowTooltip?: boolean // 当内容过长被隐藏时显示tooltip
  resizable?: boolean // 是否可调整列宽
  className?: string // 列的className
  labelClassName?: string // 当前列标题的自定义类名
  selectable?: (row: any, index: number) => boolean // 仅对type=selection的列有效
  reserveSelection?: boolean // 仅对type=selection的列有效
  filters?: Array<{ text: string; value: any }> // 数据过滤的选项
  filterPlacement?: string // 过滤弹出框的定位
  filterMultiple?: boolean // 数据过滤的选项是否多选
  filterMethod?: (value: any, row: any, column: any) => boolean // 数据过滤使用的方法
  filteredValue?: any[] // 选中的数据过滤项
}

// 操作按钮配置
export interface ActionConfig {
  label: string // 按钮文本
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text' // 按钮类型
  icon?: string // 图标
  size?: 'large' | 'default' | 'small' // 按钮大小
  plain?: boolean // 是否朴素按钮
  round?: boolean // 是否圆角按钮
  circle?: boolean // 是否圆形按钮
  loading?: boolean // 是否加载中状态
  disabled?: boolean // 是否禁用
  permission?: string // 权限标识
  show?: boolean | ((row?: any) => boolean) // 是否显示（可以是函数，用于行操作）
  handler: (row?: any, index?: number) => void // 点击处理函数
}

// 字段差异化配置
export interface FieldOverride {
  [fieldName: string]: {
    label?: string // 自定义标签
    formatter?: (value: any, row: any) => string // 自定义格式化
    component?: SearchComponentType // 自定义搜索组件
    show?: boolean // 是否显示
    [key: string]: any // 其他自定义属性
  }
}

// API配置
export interface ApiConfig {
  list: string // 列表接口
  delete?: string // 删除接口
  export?: string // 导出接口
  detail?: string // 详情接口
  create?: string // 新增接口
  update?: string // 更新接口
}

// 页面配置
export interface PageConfig {
  title: string // 页面标题
  api: ApiConfig // API配置
  searchFields: SearchFieldConfig[] // 搜索字段配置
  tableColumns: TableColumnConfig[] // 表格列配置
  topActions?: ActionConfig[] // 顶部操作按钮
  rowActions?: ActionConfig[] // 行操作按钮
  fieldOverrides?: FieldOverride // 字段差异化配置
  pagination?: {
    pageSize?: number // 每页条数，默认10
    pageSizes?: number[] // 每页显示个数选择器的选项设置，默认[10, 20, 50, 100]
    layout?: string // 组件布局，默认'total, sizes, prev, pager, next, jumper'
  }
  tableProps?: Record<string, any> // 表格额外属性
  searchProps?: Record<string, any> // 搜索表单额外属性
}

// 搜索参数
export interface SearchParams {
  [key: string]: any
  pageNum?: number
  pageSize?: number
}

// 列表响应数据
export interface ListResponse<T = any> {
  code: number
  data: {
    list: T[]
    total: number
  }
  msg: string
}