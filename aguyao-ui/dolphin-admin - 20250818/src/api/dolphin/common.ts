import request from '@/config/axios'
import type { SearchParams, ListResponse } from '@/types/generic-page'

/**
 * 通用API接口
 */
export const commonApi = {
  /**
   * 获取列表数据
   * @param url 接口地址
   * @param params 查询参数
   * @returns 列表数据
   */
  getList<T = ListResponse>(url: string, params?: SearchParams): Promise<T> {
    // 模拟数据返回
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockData = {
          code: 0,
          msg: 'success',
          data: {
            list: [
              {
                id: 1,
                taskId: '任务1',
                country: '中国',
                status: 1,
                taskCount: 100,
                priority: 'high',
                assignee: '张三',
                createTime: '2024-01-15 10:30:00',
                updateTime: '2024-01-15 14:20:00',
                description: '这是一个高优先级的任务'
              },
              {
                id: 2,
                taskId: '任务2',
                country: '中国',
                status: 0,
                taskCount: 500,
                priority: 'medium',
                assignee: '李四',
                createTime: '2024-01-14 09:15:00',
                updateTime: '2024-01-14 16:45:00',
                description: '这是一个中等优先级的任务'
              },
              {
                id: 3,
                taskId: '项目A',
                status: 1,
                type: 'web',
                manager: '王五',
                startDate: '2024-01-10',
                endDate: '2024-03-10',
                progress: 65
              },
              {
                id: 4,
                taskId: '项目B',
                status: 0,
                type: 'mobile',
                manager: '赵六',
                startDate: '2024-01-20',
                endDate: '2024-04-20',
                progress: 30
              },
              {
                id: 5,
                taskId: '用户001',
                email: '<EMAIL>',
                phone: '13800138001',
                department: '技术部',
                role: '开发工程师',
                status: 1,
                lastLogin: '2024-01-15 08:30:00'
              }
            ],
            total: 25,
            pageNo: params?.pageNo || 1,
            pageSize: params?.pageSize || 10
          }
        }
        resolve(mockData as T)
      }, 300) // 模拟网络延迟
    })
    // return request.get({ url, params })
  },

  /**
   * 获取详情数据
   * @param url 接口地址
   * @param id 数据ID
   * @returns 详情数据
   */
  getDetail<T = any>(url: string, id: string | number): Promise<T> {
    return request.get({ url: `${url}/${id}` })
  },

  /**
   * 新增数据
   * @param url 接口地址
   * @param data 新增数据
   * @returns 响应结果
   */
  create<T = any>(url: string, data: any): Promise<T> {
    return request.post({ url, data })
  },

  /**
   * 更新数据
   * @param url 接口地址
   * @param data 更新数据
   * @returns 响应结果
   */
  update<T = any>(url: string, data: any): Promise<T> {
    return request.put({ url, data })
  },

  /**
   * 删除数据
   * @param url 接口地址
   * @param ids 要删除的ID（单个或多个）
   * @returns 响应结果
   */
  delete<T = any>(url: string, ids: string | number | (string | number)[]): Promise<T> {
    const data = Array.isArray(ids) ? { ids } : { id: ids }
    return request.delete({ url, data })
  },

  /**
   * 导出数据
   * @param url 接口地址
   * @param params 查询参数
   * @param filename 文件名
   * @returns 下载文件
   */
  async export(url: string, params?: Record<string, any>, filename?: string): Promise<void> {
    try {
      const response = await request.get({
        url,
        params,
        responseType: 'blob'
      })

      // 创建下载链接
      const blob = new Blob([response as any], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      
      // 设置文件名
      const defaultFilename = `export_${new Date().getTime()}.xlsx`
      link.download = filename || defaultFilename
      
      // 触发下载
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      // 清理URL对象
      window.URL.revokeObjectURL(downloadUrl)
    } catch (error) {
      console.error('导出失败:', error)
      throw error
    }
  },

  /**
   * 批量操作
   * @param url 接口地址
   * @param action 操作类型
   * @param ids 操作的ID列表
   * @param data 额外数据
   * @returns 响应结果
   */
  batchAction<T = any>(
    url: string,
    action: string,
    ids: (string | number)[],
    data?: any
  ): Promise<T> {
    return request.post({
      url,
      data: {
        action,
        ids,
        ...data
      }
    })
  },

  /**
   * 上传文件
   * @param url 接口地址
   * @param file 文件对象
   * @param data 额外数据
   * @returns 响应结果
   */
  upload<T = any>(url: string, file: File, data?: Record<string, any>): Promise<T> {
    const formData = new FormData()
    formData.append('file', file)
    
    if (data) {
      Object.entries(data).forEach(([key, value]) => {
        formData.append(key, value as string)
      })
    }

    return request.post({
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 获取选项数据（用于下拉框等）
   * @param url 接口地址
   * @param params 查询参数
   * @returns 选项数据
   */
  getOptions<T = Array<{ label: string; value: any }>>(url: string, params?: Record<string, any>): Promise<T> {
    return request.get({ url, params })
  },

  /**
   * 状态切换
   * @param url 接口地址
   * @param id 数据ID
   * @param status 新状态
   * @returns 响应结果
   */
  toggleStatus<T = any>(url: string, id: string | number, status: any): Promise<T> {
    return request.put({
      url: `${url}/${id}/status`,
      data: { status }
    })
  },

  /**
   * 获取统计数据
   * @param url 接口地址
   * @param params 查询参数
   * @returns 统计数据
   */
  getStatistics<T = any>(url: string, params?: Record<string, any>): Promise<T> {
    return request.get({ url, params })
  }
}

export default commonApi
