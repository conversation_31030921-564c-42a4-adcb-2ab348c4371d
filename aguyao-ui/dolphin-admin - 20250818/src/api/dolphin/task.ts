import { commonApi } from './common'
import type { ListResponse } from '@/types/generic-page'

// 任务数据类型
export interface TaskItem {
  id: string | number
  taskName: string
  description?: string
  status: number // 0-待处理 1-进行中 2-已完成 3-已取消
  priority: number // 1-低 2-中 3-高 4-紧急
  assignee: string
  progress: number
  createTime: string
  dueDate?: string
  updateTime?: string
  creator?: string
  [key: string]: any
}

// 任务查询参数
export interface TaskSearchParams {
  taskName?: string
  status?: number
  priority?: number
  assignee?: string
  createTimeStart?: string
  createTimeEnd?: string
  dueDateStart?: string
  dueDateEnd?: string
  pageNum?: number
  pageSize?: number
  orderBy?: string
  orderDirection?: 'asc' | 'desc'
}

// 任务创建/更新数据
export interface TaskFormData {
  taskName: string
  description?: string
  status?: number
  priority: number
  assignee: string
  dueDate?: string
  progress?: number
}

/**
 * 任务管理API
 */
export const taskApi = {
  /**
   * 获取任务列表
   * @param params 查询参数
   * @returns 任务列表
   */
  getList(params?: TaskSearchParams): Promise<ListResponse<TaskItem>> {
    return commonApi.getList('/api/dolphin/task/list', params)
  },

  /**
   * 获取任务详情
   * @param id 任务ID
   * @returns 任务详情
   */
  getDetail(id: string | number): Promise<{ code: number; data: TaskItem; msg: string }> {
    return commonApi.getDetail('/api/dolphin/task/detail', id)
  },

  /**
   * 创建任务
   * @param data 任务数据
   * @returns 创建结果
   */
  create(data: TaskFormData): Promise<{ code: number; data: any; msg: string }> {
    return commonApi.create('/api/dolphin/task/create', data)
  },

  /**
   * 更新任务
   * @param id 任务ID
   * @param data 任务数据
   * @returns 更新结果
   */
  update(id: string | number, data: TaskFormData): Promise<{ code: number; data: any; msg: string }> {
    return commonApi.update(`/api/dolphin/task/update/${id}`, data)
  },

  /**
   * 删除任务
   * @param ids 任务ID（单个或多个）
   * @returns 删除结果
   */
  delete(ids: string | number | (string | number)[]): Promise<{ code: number; data: any; msg: string }> {
    return commonApi.delete('/api/dolphin/task/delete', ids)
  },

  /**
   * 导出任务数据
   * @param params 查询参数
   * @param filename 文件名
   * @returns 导出文件
   */
  export(params?: TaskSearchParams, filename?: string): Promise<void> {
    return commonApi.export('/api/dolphin/task/export', params, filename || `tasks_${new Date().getTime()}.xlsx`)
  },

  /**
   * 更新任务状态
   * @param id 任务ID
   * @param status 新状态
   * @returns 更新结果
   */
  updateStatus(id: string | number, status: number): Promise<{ code: number; data: any; msg: string }> {
    return commonApi.toggleStatus('/api/dolphin/task', id, status)
  },

  /**
   * 更新任务进度
   * @param id 任务ID
   * @param progress 进度百分比
   * @returns 更新结果
   */
  updateProgress(id: string | number, progress: number): Promise<{ code: number; data: any; msg: string }> {
    return commonApi.update(`/api/dolphin/task/${id}/progress`, { progress })
  },

  /**
   * 批量更新任务状态
   * @param ids 任务ID列表
   * @param status 新状态
   * @returns 更新结果
   */
  batchUpdateStatus(ids: (string | number)[], status: number): Promise<{ code: number; data: any; msg: string }> {
    return commonApi.batchAction('/api/dolphin/task/batch', 'updateStatus', ids, { status })
  },

  /**
   * 批量分配任务
   * @param ids 任务ID列表
   * @param assignee 负责人
   * @returns 分配结果
   */
  batchAssign(ids: (string | number)[], assignee: string): Promise<{ code: number; data: any; msg: string }> {
    return commonApi.batchAction('/api/dolphin/task/batch', 'assign', ids, { assignee })
  },

  /**
   * 获取任务统计数据
   * @param params 查询参数
   * @returns 统计数据
   */
  getStatistics(params?: Record<string, any>): Promise<{
    code: number
    data: {
      total: number
      pending: number
      inProgress: number
      completed: number
      cancelled: number
      overdueCount: number
    }
    msg: string
  }> {
    return commonApi.getStatistics('/api/dolphin/task/statistics', params)
  },

  /**
   * 获取负责人列表
   * @returns 负责人选项
   */
  getAssigneeOptions(): Promise<{ code: number; data: Array<{ label: string; value: string }>; msg: string }> {
    return commonApi.getOptions('/api/dolphin/task/assignee-options')
  },

  /**
   * 复制任务
   * @param id 源任务ID
   * @param data 新任务数据（可选）
   * @returns 复制结果
   */
  copy(id: string | number, data?: Partial<TaskFormData>): Promise<{ code: number; data: any; msg: string }> {
    return commonApi.create(`/api/dolphin/task/${id}/copy`, data || {})
  },

  /**
   * 任务归档
   * @param ids 任务ID列表
   * @returns 归档结果
   */
  archive(ids: (string | number)[]): Promise<{ code: number; data: any; msg: string }> {
    return commonApi.batchAction('/api/dolphin/task/batch', 'archive', ids)
  },

  /**
   * 获取任务历史记录
   * @param id 任务ID
   * @returns 历史记录
   */
  getHistory(id: string | number): Promise<{
    code: number
    data: Array<{
      id: string
      action: string
      description: string
      operator: string
      createTime: string
    }>
    msg: string
  }> {
    return commonApi.getList(`/api/dolphin/task/${id}/history`)
  }
}

export default taskApi