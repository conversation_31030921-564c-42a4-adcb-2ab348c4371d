<template>
  <div class="dynamic-route-test">
    <h2>动态路由测试页面</h2>
    <p>当前路由路径: {{ route.path }}</p>
    <p>路由 meta.pageKey: {{ route.meta?.pageKey }}</p>
    <p>推断的 pageKey: {{ inferredPageKey }}</p>
    
    <div class="test-links">
      <h3>测试链接:</h3>
      <ul>
        <li><router-link to="/dolphin/task">任务管理 (静态路由)</router-link></li>
        <li><router-link to="/dolphin/project">项目管理 (静态路由)</router-link></li>
        <li><router-link to="/dolphin/dynamic-test">动态测试路由</router-link></li>
      </ul>
    </div>
    
    <!-- 使用 GenericListPage 组件测试 -->
    <div class="generic-page-test">
      <h3>GenericListPage 组件测试:</h3>
      <GenericListPage />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import GenericListPage from '@/views/dolphin/common/GenericListPage.vue'

const route = useRoute()

// 模拟推断 pageKey 的逻辑
const inferredPageKey = computed(() => {
  let key = route.meta?.pageKey as string
  
  if (!key) {
    const pathSegments = route.path.split('/')
    key = pathSegments[pathSegments.length - 1] || pathSegments[pathSegments.length - 2]
    
    if (route.path.startsWith('/dolphin/')) {
      const dolphinKey = route.path.replace('/dolphin/', '').split('/')[0]
      if (dolphinKey) {
        key = dolphinKey
      }
    }
  }
  
  return key
})
</script>

<style scoped>
.dynamic-route-test {
  padding: 20px;
}

.test-links ul {
  list-style-type: none;
  padding: 0;
}

.test-links li {
  margin: 10px 0;
}

.test-links a {
  color: #409eff;
  text-decoration: none;
  padding: 5px 10px;
  border: 1px solid #409eff;
  border-radius: 4px;
  display: inline-block;
}

.test-links a:hover {
  background-color: #409eff;
  color: white;
}

.generic-page-test {
  margin-top: 30px;
  border-top: 1px solid #eee;
  padding-top: 20px;
}
</style>