<template>
  <div class="generic-list-page">
    <!-- 搜索区域 -->
    <GenericSearch
      v-if="pageConfig?.searchFields?.length"
      :fields="pageConfig.searchFields"
      :loading="loading"
      :search-props="pageConfig.searchProps"
      @search="handleSearch"
      @reset="handleReset"
      ref="searchRef"
    />
    
    <!-- 内容区域 -->
    <div class="content-container">
      <!-- 顶部操作栏 -->
      <div class="top-actions" v-if="pageConfig?.topActions?.length">
        <div class="actions-left">
          <el-button
            v-for="action in visibleTopActions"
            :key="action.label"
            :type="action.type || 'default'"
            :size="action.size || 'default'"
            :plain="action.plain"
            :round="action.round"
            :circle="action.circle"
            :loading="action.loading"
            :disabled="action.disabled"
            @click="action.handler"
          >
            <Icon v-if="action.icon" :icon="action.icon" class="mr-5px" />
            {{ action.label }}
          </el-button>
        </div>
        <div class="actions-right">
          <!-- 可以在这里添加其他操作，如刷新、设置等 -->
          <el-button
            type="text"
            @click="handleRefresh"
            :loading="loading"
            title="刷新"
          >
            <Icon icon="ep:refresh" />
          </el-button>
        </div>
      </div>
      
      <!-- 表格 -->
      <el-table
        ref="tableRef"
        :data="tableData"
        :loading="loading"
        v-bind="pageConfig?.tableProps"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <!-- 动态列 -->
        <el-table-column
          v-for="column in visibleColumns"
          :key="column.prop"
          v-bind="column"
        >
          <!-- 自定义列内容 -->
          <template #default="{ row, column: col, $index }" v-if="column.formatter">
            <span v-html="column.formatter(row, col, row[column.prop], $index)"></span>
          </template>
          
          <!-- 字段差异化处理 -->
          <template #default="{ row }" v-else-if="hasFieldOverride(column.prop)">
            <span v-html="formatFieldValue(column.prop, row[column.prop], row)"></span>
          </template>
        </el-table-column>
        
        <!-- 操作列 -->
        <el-table-column
          v-if="pageConfig?.rowActions?.length"
          label="操作"
          fixed="right"
          width="120"
          align="center"
        >
          <template #default="{ row, $index }">
            <div class="row-actions">
              <template v-for="action in getVisibleRowActions(row)" :key="action.label">
                <el-button
                  :type="action.type || 'text'"
                  :size="action.size || 'small'"
                  :plain="action.plain"
                  :round="action.round"
                  :circle="action.circle"
                  :loading="action.loading"
                  :disabled="action.disabled"
                  @click="action.handler(row, $index)"
                  class="action-button"
                >
                  <Icon v-if="action.icon" :icon="action.icon" class="mr-5px" />
                  {{ action.label }}
                </el-button>
              </template>
            </div>
          </template>
        </el-table-column>
        
        <!-- 空数据 -->
        <template #empty>
          <el-empty description="暂无数据" />
        </template>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="pageConfig?.pagination?.pageSizes || [10, 20, 50, 100]"
          :layout="pageConfig?.pagination?.layout || 'total, sizes, prev, pager, next, jumper'"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import type { PageConfig, SearchParams, ListResponse, TableColumnConfig, ActionConfig } from '@/types/generic-page'
import { getPageConfig } from './configs'
import { commonApi } from '@/api/dolphin/common'
import GenericSearch from '@/components/Search/GenericSearch.vue'
import { Icon } from '@/components/Icon'
import { ElMessage, ElMessageBox } from 'element-plus'

interface Props {
  pageKey?: string // 页面配置key
  config?: PageConfig // 直接传入配置
}

const props = defineProps<Props>()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const tableData = ref<any[]>([])
const total = ref(0)
const selectedRows = ref<any[]>([])
const searchRef = ref()
const tableRef = ref()

// 查询参数
const queryParams = reactive<SearchParams>({
  pageNum: 1,
  pageSize: 10
})

// 获取页面配置
const pageConfig = computed<PageConfig | null>(() => {
  if (props.config) {
    return props.config
  }
  
  let key = props.pageKey || route.meta?.pageKey as string
  
  // 如果没有找到 key，尝试从路由路径中推断
  if (!key) {
    const pathSegments = route.path.split('/')
    // 从路径中获取最后一个非空段作为 key
    key = pathSegments[pathSegments.length - 1] || pathSegments[pathSegments.length - 2]
    
    // 如果路径是 /dolphin/xxx 格式，使用 xxx 作为 key
    if (route.path.startsWith('/dolphin/')) {
      const dolphinKey = route.path.replace('/dolphin/', '').split('/')[0]
      if (dolphinKey) {
        key = dolphinKey
      }
    }
  }
  
  if (!key) {
    console.error('未找到页面配置key，路由路径:', route.path)
    return null
  }
  
  console.log('使用页面配置key:', key, '路由路径:', route.path)
  return getPageConfig(key)
})

// 计算可见列
const visibleColumns = computed<TableColumnConfig[]>(() => {
  if (!pageConfig.value?.tableColumns) return []
  
  return pageConfig.value.tableColumns.filter(column => {
    // 检查字段差异化配置
    const override = pageConfig.value?.fieldOverrides?.[column.prop]
    if (override?.show === false) return false
    
    return column.show !== false
  }).map(column => {
    // 应用字段差异化配置
    const override = pageConfig.value?.fieldOverrides?.[column.prop]
    if (override) {
      return {
        ...column,
        label: override.label || column.label,
        ...override
      }
    }
    return column
  })
})

// 计算可见的顶部操作
const visibleTopActions = computed<ActionConfig[]>(() => {
  if (!pageConfig.value?.topActions) return []
  
  return pageConfig.value.topActions.filter(action => {
    if (typeof action.show === 'function') {
      return action.show()
    }
    return action.show !== false
  })
})

// 获取可见的行操作
const getVisibleRowActions = (row: any): ActionConfig[] => {
  if (!pageConfig.value?.rowActions) return []
  
  return pageConfig.value.rowActions.filter(action => {
    if (typeof action.show === 'function') {
      return action.show(row)
    }
    return action.show !== false
  })
}

// 检查是否有字段差异化配置
const hasFieldOverride = (fieldName: string): boolean => {
  return !!pageConfig.value?.fieldOverrides?.[fieldName]?.formatter
}

// 格式化字段值
const formatFieldValue = (fieldName: string, value: any, row: any): string => {
  const override = pageConfig.value?.fieldOverrides?.[fieldName]
  if (override?.formatter) {
    return override.formatter(value, row)
  }
  return value
}

// 获取列表数据
const getList = async () => {
  if (!pageConfig.value?.api?.list) {
    console.error('未配置列表API')
    return
  }
  
  try {
    loading.value = true
    const response = await commonApi.getList<ListResponse>(pageConfig.value.api.list, queryParams)
    
    if (response.code === 0) {
      tableData.value = response.data.list || []
      total.value = response.data.total || 0
    } else {
      ElMessage.error(response.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取列表数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = (params: Record<string, any>) => {
  Object.assign(queryParams, params)
  queryParams.pageNum = 1 // 重置到第一页
  getList()
}

// 重置
const handleReset = () => {
  // 清空搜索参数，保留分页参数
  Object.keys(queryParams).forEach(key => {
    if (key !== 'pageNum' && key !== 'pageSize') {
      delete queryParams[key]
    }
  })
  queryParams.pageNum = 1
  getList()
}

// 刷新
const handleRefresh = () => {
  getList()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size
  queryParams.pageNum = 1
  getList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  queryParams.pageNum = page
  getList()
}

// 选择改变
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 排序改变
const handleSortChange = ({ column, prop, order }: any) => {
  if (order) {
    queryParams.orderBy = prop
    queryParams.orderDirection = order === 'ascending' ? 'asc' : 'desc'
  } else {
    delete queryParams.orderBy
    delete queryParams.orderDirection
  }
  getList()
}

// 删除确认
const handleDelete = async (ids: string | string[], successCallback?: () => void) => {
  if (!pageConfig.value?.api?.delete) {
    ElMessage.error('未配置删除API')
    return
  }
  
  try {
    await ElMessageBox.confirm('确定要删除选中的数据吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await commonApi.delete(pageConfig.value.api.delete, ids)
    
    if (response.code === 0) {
      ElMessage.success('删除成功')
      getList()
      successCallback?.()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 导出
const handleExport = async (params?: Record<string, any>) => {
  if (!pageConfig.value?.api?.export) {
    ElMessage.error('未配置导出API')
    return
  }
  
  try {
    loading.value = true
    const exportParams = { ...queryParams, ...params }
    delete exportParams.pageNum
    delete exportParams.pageSize
    
    await commonApi.export(pageConfig.value.api.export, exportParams)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    loading.value = false
  }
}

// 监听页面配置变化
watch(
  () => pageConfig.value,
  (newConfig) => {
    if (newConfig) {
      // 重置查询参数
      Object.keys(queryParams).forEach(key => {
        if (key !== 'pageNum' && key !== 'pageSize') {
          delete queryParams[key]
        }
      })
      queryParams.pageNum = 1
      queryParams.pageSize = newConfig.pagination?.pageSize || 10
      
      // 获取数据
      getList()
    }
  },
  { immediate: true }
)

onMounted(() => {
  if (pageConfig.value) {
    getList()
  }
})

// 暴露方法给父组件
defineExpose({
  refresh: handleRefresh,
  search: handleSearch,
  reset: handleReset,
  delete: handleDelete,
  export: handleExport,
  getSelectedRows: () => selectedRows.value,
  getTableData: () => tableData.value
})
</script>

<style scoped>
.generic-list-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.content-container {
  flex: 1;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.top-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
}

.actions-left {
  display: flex;
  gap: 8px;
}

.actions-right {
  display: flex;
  gap: 8px;
}

.row-actions {
  display: flex;
  /* gap: 8px; */
  justify-content: center;
  flex-wrap: wrap;
}

.action-button {
  padding: 2px 4px;
}

.pagination-container {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #ebeef5;
}

:deep(.el-table) {
  flex: 1;
}

:deep(.el-table__body-wrapper) {
  flex: 1;
}
</style>
