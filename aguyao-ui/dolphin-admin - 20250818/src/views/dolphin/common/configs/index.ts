import type { PageConfig } from '@/types/generic-page'

// 页面配置注册表
const pageConfigs = new Map<string, PageConfig>()

/**
 * 注册页面配置
 * @param key 页面配置key
 * @param config 页面配置
 */
export const registerPageConfig = (key: string, config: PageConfig): void => {
  pageConfigs.set(key, config)
}

/**
 * 获取页面配置
 * @param key 页面配置key
 * @returns 页面配置
 */
export const getPageConfig = (key: string): PageConfig | undefined => {
  return pageConfigs.get(key)
}

/**
 * 获取所有页面配置
 * @returns 所有页面配置
 */
export const getAllPageConfigs = (): Map<string, PageConfig> => {
  return pageConfigs
}

/**
 * 批量注册页面配置
 * @param configs 配置对象
 */
export const registerPageConfigs = (configs: Record<string, PageConfig>): void => {
  Object.entries(configs).forEach(([key, config]) => {
    registerPageConfig(key, config)
  })
}

// 自动导入所有配置文件
const configModules = import.meta.glob('./*.config.ts', { eager: true })

Object.entries(configModules).forEach(([path, module]) => {
  const fileName = path.replace('./', '').replace('.config.ts', '')
  const config = (module as any).default
  
  if (config && typeof config === 'object') {
    registerPageConfig(fileName, config)
    console.log(`已注册页面配置: ${fileName}`)
  }
})

export default {
  registerPageConfig,
  getPageConfig,
  getAllPageConfigs,
  registerPageConfigs
}