import type { PageConfig } from '@/types/generic-page'
import { ElMessage } from 'element-plus'

// 任务状态选项
const taskStatusOptions = [
  { label: '已完成', value: 2 },
  { label: '进行中', value: 1 },
  { label: '排队中', value: 0 },
  { label: '已关闭', value: 3 }
]

// 优先级选项
const priorityOptions = [
  { label: '低', value: 1 },
  { label: '中', value: 2 },
  { label: '高', value: 3 },
  { label: '紧急', value: 4 }
]

// 任务管理页面配置
const taskConfig: PageConfig = {
  title: '任务管理',
  
  // API配置
  api: {
    list: '/api/dolphin/task/list',
    delete: '/api/dolphin/task/delete',
    export: '/api/dolphin/task/export',
    detail: '/api/dolphin/task/detail',
    create: '/api/dolphin/task/create',
    update: '/api/dolphin/task/update'
  },
  
  // 搜索字段配置
  searchFields: [
    {
      key: 'taskId',
      label: '任务ID',
      component: 'input',
      placeholder: '请输入任务ID',
      span: 5
    },
    // {
    //   key: 'taskName',
    //   label: '任务名称',
    //   component: 'input',
    //   placeholder: '请输入任务名称',
    //   span: 6
    // },
    {
      key: 'description',
      label: '描述',
      component: 'input',
      placeholder: '请输入描述',
      span: 6
    },
    {
      key: 'country',
      label: '国家',
      component: 'input',
      placeholder: '请输入国家',
      span: 6
    },
    {
      key: 'status',
      label: '任务状态',
      component: 'select',
      options: taskStatusOptions,
      placeholder: '请选择任务状态',
      span: 6
    }
    
  ],
  
  // 表格列配置
  tableColumns: [
    {
      type: 'selection',
      label: '',
      width: 50,
      prop: 'selection'
    },
    // {
    //   type: 'index',
    //   label: '序号',
    //   width: 60,
    //   prop: 'index'
    // },
    {
      prop: 'description',
      label: '描述',
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      prop: 'taskId',
      label: '任务ID',
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      prop: 'country',
      label: '国家',
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      prop: 'taskCount',
      label: '任务数量',
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      prop: 'progress',
      label: '进度',
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      prop: 'validCount',
      label: '有效数量',
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      prop: 'status',
      label: '任务状态',
      width: 100,
      align: 'center',
      formatter: (row, column, cellValue) => {
        const status = taskStatusOptions.find(item => item.value === cellValue)
        const colorMap = {
          0: '#909399', // 待处理 - 灰色
          1: '#409EFF', // 进行中 - 蓝色
          2: '#67C23A', // 已完成 - 绿色
          3: '#F56C6C'  // 已取消 - 红色
        }
        const color = colorMap[cellValue as keyof typeof colorMap] || '#909399'
        return `<span style="color: ${color}; font-weight: 500;">${status?.label || '未知'}</span>`
      }
    },
    {
      prop: 'balance',
      label: '余额扣费',
      minWidth: 120,
      showOverflowTooltip: true
    },
    // {
    //   prop: 'priority',
    //   label: '优先级',
    //   width: 100,
    //   align: 'center',
    //   formatter: (row, column, cellValue) => {
    //     const priority = priorityOptions.find(item => item.value === cellValue)
    //     const colorMap = {
    //       1: '#909399', // 低 - 灰色
    //       2: '#E6A23C', // 中 - 橙色
    //       3: '#F56C6C', // 高 - 红色
    //       4: '#F56C6C'  // 紧急 - 红色加粗
    //     }
    //     const color = colorMap[cellValue as keyof typeof colorMap] || '#909399'
    //     const fontWeight = cellValue === 4 ? 'bold' : 'normal'
    //     return `<span style="color: ${color}; font-weight: ${fontWeight};">${priority?.label || '未知'}</span>`
    //   }
    // },
    {
      prop: 'createUser',
      label: '创建人',
      width: 120,
      align: 'center'
    },
    // {
    //   prop: 'progress',
    //   label: '进度',
    //   width: 120,
    //   align: 'center',
    //   formatter: (row, column, cellValue) => {
    //     const progress = cellValue || 0
    //     const color = progress >= 80 ? '#67C23A' : progress >= 50 ? '#E6A23C' : '#F56C6C'
    //     return `<span style="color: ${color}; font-weight: 500;">${progress}%</span>`
    //   }
    // },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 160,
      align: 'center',
      sortable: true
    },
    {
      prop: 'completeTime',
      label: '完成时间',
      width: 160,
      align: 'center',
      sortable: true
    }
    // {
    //   prop: 'dueDate',
    //   label: '截止日期',
    //   width: 160,
    //   align: 'center',
    //   sortable: true,
    //   formatter: (row, column, cellValue) => {
    //     if (!cellValue) return '-'
    //     const dueDate = new Date(cellValue)
    //     const now = new Date()
    //     const isOverdue = dueDate < now && row.status !== 2 // 未完成且已过期
    //     const color = isOverdue ? '#F56C6C' : '#606266'
    //     return `<span style="color: ${color};">${cellValue}</span>`
    //   }
    // }
  ],
  
  // 顶部操作按钮
  topActions: [
    {
      label: '新增任务',
      type: 'primary',
      icon: 'ep:plus',
      handler: () => {
        // 这里可以打开新增对话框或跳转到新增页面
        ElMessage.info('打开新增任务对话框')
      }
    },
    {
      label: '批量删除',
      type: 'danger',
      icon: 'ep:delete',
      handler: () => {
        // 这里可以获取选中的行数据进行批量删除
        ElMessage.info('执行批量删除操作')
      }
    },
    {
      label: '导出数据',
      type: 'success',
      icon: 'ep:download',
      handler: () => {
        // 这里可以调用导出方法
        ElMessage.info('开始导出数据')
      }
    }
  ],
  
  // 行操作按钮
  rowActions: [
    {
      label: '仅手机号',
      type: 'text',
      // icon: 'ep:view',
      handler: (row) => {
        ElMessage.info(`查看任务: ${row.taskId}`)
      }
    },
    {
      label: '全量数据',
      type: 'text',
      // icon: 'ep:edit',
      show: (row) => row.status !== 2, // 已完成的任务不显示编辑按钮
      handler: (row) => {
        ElMessage.info(`编辑任务: ${row.taskId}`)
      }
    },
    {
      label: '非开通号码',
      type: 'text',
      // icon: 'ep:delete',
      handler: (row) => {
        ElMessage.info(`删除任务: ${row.taskId}`)
      }
    },
    // {
    //   label: '完成',
    //   type: 'text',
    //   icon: 'ep:check',
    //   show: (row) => row.status === 1, // 只有进行中的任务显示完成按钮
    //   handler: (row) => {
    //     ElMessage.success(`任务 ${row.taskName} 已完成`)
    //   }
    // }
  ],
  
  // 分页配置
  pagination: {
    pageSize: 20,
    pageSizes: [10, 20, 50, 100],
    layout: 'total, sizes, prev, pager, next, jumper'
  },
  
  // 表格额外属性
  tableProps: {
    stripe: true,
    border: true,
    size: 'default',
    highlightCurrentRow: true
  },
  
  // 搜索表单额外属性
  searchProps: {
    labelWidth: '80px',
    size: 'default'
  }
}

export default taskConfig
