# 通用列表页面使用文档

## 概述

通用列表页面组件是一个高度可配置的列表页面解决方案，通过配置驱动的方式，可以快速生成功能完整的列表页面。该方案特别适用于具有相似结构的多个页面，能够大大减少重复代码，提高开发效率。

## 核心特性

- **配置驱动**: 通过JSON配置即可生成完整的列表页面
- **高度复用**: 一套代码支持多种页面类型
- **类型安全**: 完整的TypeScript类型定义
- **灵活扩展**: 支持字段差异化配置和自定义组件
- **功能完整**: 内置搜索、分页、排序、导出、批量操作等功能

## 快速开始

### 1. 创建页面配置

在 `src/views/dolphin/common/configs/` 目录下创建配置文件，例如 `example.config.ts`：

```typescript
import type { PageConfig } from '@/types/generic-page'

const exampleConfig: PageConfig = {
  title: '示例管理',
  api: {
    list: '/api/example/list',
    delete: '/api/example/delete',
    export: '/api/example/export'
  },
  searchFields: [
    {
      key: 'name',
      label: '名称',
      component: 'input',
      placeholder: '请输入名称'
    },
    {
      key: 'status',
      label: '状态',
      component: 'select',
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    }
  ],
  tableColumns: [
    { prop: 'name', label: '名称', minWidth: 150 },
    { prop: 'status', label: '状态', width: 100 },
    { prop: 'createTime', label: '创建时间', width: 160 }
  ],
  topActions: [
    {
      label: '新增',
      type: 'primary',
      icon: 'ep:plus',
      handler: () => console.log('新增')
    }
  ],
  rowActions: [
    {
      label: '编辑',
      type: 'text',
      handler: (row) => console.log('编辑', row)
    }
  ]
}

export default exampleConfig
```

### 2. 配置路由

在路由配置中添加页面，指定 `pageKey`：

```typescript
{
  path: 'example',
  component: () => import('@/views/dolphin/common/GenericListPage.vue'),
  name: 'Example',
  meta: {
    title: '示例管理',
    pageKey: 'example' // 对应配置文件名
  }
}
```

### 3. 完成

现在访问该路由即可看到完整的列表页面！

## 配置详解

### PageConfig 接口

```typescript
interface PageConfig {
  title: string // 页面标题
  api: ApiConfig // API配置
  searchFields: SearchFieldConfig[] // 搜索字段
  tableColumns: TableColumnConfig[] // 表格列
  topActions?: ActionConfig[] // 顶部操作按钮
  rowActions?: ActionConfig[] // 行操作按钮
  fieldOverrides?: FieldOverride // 字段差异化配置
  pagination?: PaginationConfig // 分页配置
  tableProps?: Record<string, any> // 表格属性
  searchProps?: Record<string, any> // 搜索表单属性
}
```

### API配置 (ApiConfig)

```typescript
interface ApiConfig {
  list: string // 列表接口 (必需)
  delete?: string // 删除接口
  export?: string // 导出接口
  detail?: string // 详情接口
  create?: string // 新增接口
  update?: string // 更新接口
}
```

### 搜索字段配置 (SearchFieldConfig)

```typescript
interface SearchFieldConfig {
  key: string // 字段名
  label: string // 显示标签
  component: SearchComponentType // 组件类型
  placeholder?: string // 占位符
  options?: Array<{ label: string; value: any }> // 选项数据
  props?: Record<string, any> // 组件属性
  defaultValue?: any // 默认值
  span?: number // 栅格占位，默认6
  show?: boolean // 是否显示，默认true
}
```

支持的搜索组件类型：
- `input`: 输入框
- `select`: 下拉选择
- `date`: 日期选择
- `daterange`: 日期范围选择
- `datetime`: 日期时间选择
- `datetimerange`: 日期时间范围选择
- `number`: 数字输入框
- `cascader`: 级联选择
- `tree-select`: 树形选择
- `radio`: 单选框组
- `checkbox`: 多选框组

### 表格列配置 (TableColumnConfig)

```typescript
interface TableColumnConfig {
  prop: string // 字段名
  label: string // 列标题
  width?: string | number // 列宽
  minWidth?: string | number // 最小列宽
  fixed?: boolean | 'left' | 'right' // 固定列
  sortable?: boolean // 是否可排序
  formatter?: (row, column, cellValue, index) => string // 格式化函数
  show?: boolean // 是否显示
  type?: 'selection' | 'index' | 'expand' // 特殊列类型
  align?: 'left' | 'center' | 'right' // 对齐方式
  // ... 更多ElementPlus表格列属性
}
```

### 操作按钮配置 (ActionConfig)

```typescript
interface ActionConfig {
  label: string // 按钮文本
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text'
  icon?: string // 图标
  size?: 'large' | 'default' | 'small'
  show?: boolean | ((row?) => boolean) // 是否显示
  handler: (row?, index?) => void // 点击处理函数
  // ... 更多ElementPlus按钮属性
}
```

## 高级功能

### 字段差异化配置

当某些页面的字段需要特殊处理时，可以使用 `fieldOverrides` 配置：

```typescript
fieldOverrides: {
  status: {
    label: '自定义状态标签',
    formatter: (value, row) => {
      return value === 1 ? '<span style="color: green">启用</span>' : '<span style="color: red">禁用</span>'
    },
    show: true
  },
  specialField: {
    show: false // 隐藏某个字段
  }
}
```

### 自定义格式化

表格列支持自定义格式化函数：

```typescript
{
  prop: 'status',
  label: '状态',
  formatter: (row, column, cellValue, index) => {
    const statusMap = { 1: '启用', 0: '禁用' }
    const colorMap = { 1: 'green', 0: 'red' }
    return `<span style="color: ${colorMap[cellValue]}">${statusMap[cellValue]}</span>`
  }
}
```

### 条件显示

操作按钮支持条件显示：

```typescript
{
  label: '编辑',
  type: 'text',
  show: (row) => row.status === 1, // 只有启用状态才显示编辑按钮
  handler: (row) => this.handleEdit(row)
}
```

### 分页配置

```typescript
pagination: {
  pageSize: 20, // 默认每页条数
  pageSizes: [10, 20, 50, 100], // 每页条数选项
  layout: 'total, sizes, prev, pager, next, jumper' // 分页组件布局
}
```

## API接口规范

### 列表接口

**请求参数：**
```typescript
{
  pageNum: number // 页码
  pageSize: number // 每页条数
  // ... 其他搜索参数
}
```

**响应格式：**
```typescript
{
  code: 0,
  data: {
    list: [], // 数据列表
    total: 100 // 总条数
  },
  msg: 'success'
}
```

### 删除接口

**请求参数：**
```typescript
{
  id: string | number // 单个删除
  // 或
  ids: (string | number)[] // 批量删除
}
```

### 导出接口

返回文件流，组件会自动处理下载。

## 扩展功能

### 自定义组件

如果需要完全自定义的页面，可以直接使用独立的Vue组件，而不使用通用组件。

### 混合使用

可以在通用组件的基础上，通过插槽等方式添加自定义内容。

### 组件方法

通用组件暴露了以下方法供父组件调用：

```typescript
// 获取组件引用
const listPageRef = ref()

// 刷新列表
listPageRef.value.refresh()

// 执行搜索
listPageRef.value.search(params)

// 重置搜索
listPageRef.value.reset()

// 获取选中行
const selectedRows = listPageRef.value.getSelectedRows()

// 获取表格数据
const tableData = listPageRef.value.getTableData()
```

## 最佳实践

1. **配置文件命名**: 使用有意义的名称，与路由的 `pageKey` 保持一致
2. **API接口**: 遵循统一的接口规范，便于通用组件处理
3. **权限控制**: 在操作按钮的 `show` 属性中添加权限判断
4. **错误处理**: 在操作处理函数中添加适当的错误处理
5. **性能优化**: 对于大量数据的页面，考虑使用虚拟滚动等优化方案

## 常见问题

### Q: 如何添加新的搜索组件类型？
A: 在 `GenericSearch.vue` 组件中添加新的组件类型判断和渲染逻辑。

### Q: 如何自定义表格样式？
A: 通过 `tableProps` 配置传入ElementPlus表格的属性，或者使用CSS覆盖样式。

### Q: 如何处理复杂的业务逻辑？
A: 对于复杂的业务逻辑，建议创建独立的页面组件，而不是强行使用通用组件。

### Q: 如何实现权限控制？
A: 在操作按钮的 `show` 属性中添加权限判断逻辑。

## 总结

通用列表页面组件通过配置驱动的方式，实现了"一套代码，多种配置"的目标，大大提高了开发效率。对于70个相似的页面，只需要创建70个配置文件即可，维护成本极低。

该方案具有以下优势：
- **开发效率高**: 新增页面只需配置，无需编写代码
- **维护成本低**: 统一的代码逻辑，便于维护和升级
- **一致性好**: 所有页面具有统一的交互和样式
- **扩展性强**: 支持字段差异化和自定义扩展
- **类型安全**: 完整的TypeScript支持

## 概述

本方案提供了一套完整的配置驱动的列表页面解决方案，通过配置文件即可快速生成具有搜索、列表、分页、操作等功能的页面。

## 快速开始

### 1. 创建配置文件

在 `src/views/dolphin/common/configs/` 目录下创建新的配置文件：

```typescript
// your-page.config.ts
import type { PageConfig } from '@/types/generic-page'

export const yourPageConfig: PageConfig = {
  title: '您的页面标题',
  api: {
    list: '/api/your-endpoint'
  },
  searchFields: [
    // 搜索字段配置
  ],
  tableColumns: [
    // 表格列配置
  ],
  actions: [
    // 操作按钮配置
  ]
}
```

### 2. 注册配置

在 `src/views/dolphin/common/configs/index.ts` 中注册您的配置：

```typescript
import { yourPageConfig } from './your-page.config'

const configMap: Record<string, PageConfig> = {
  // ... 其他配置
  yourPage: yourPageConfig
}
```

### 3. 添加路由

在路由配置中添加新页面：

```typescript
{
  path: 'your-page',
  component: () => import('@/views/dolphin/common/GenericListPage.vue'),
  name: 'YourPage',
  meta: {
    title: '您的页面',
    pageKey: 'yourPage'  // 对应配置中的key
  }
}
```

## 配置说明

### 搜索字段配置

支持的组件类型：
- `Input`: 输入框
- `Select`: 下拉选择
- `DatePicker`: 日期选择
- `DateRange`: 日期范围选择
- `InputNumber`: 数字输入
- `Switch`: 开关
- `Cascader`: 级联选择

### 表格列配置

支持的配置项：
- `field`: 字段名
- `label`: 列标题
- `width`: 列宽度
- `formatter`: 格式化函数
- `sortable`: 是否可排序

### 操作按钮配置

内置操作类型：
- `detail`: 详情
- `edit`: 编辑
- `delete`: 删除
- `export`: 导出

## 扩展功能

### 自定义操作

可以通过监听组件事件来处理自定义操作：

```vue
<GenericListPage
  :page-key="pageKey"
  @custom-action="handleCustomAction"
/>
```

### 字段差异化

通过 `fieldOverrides` 配置实现字段的差异化处理：

```typescript
fieldOverrides: [
  {
    field: 'specialField',
    show: false  // 隐藏字段
  },
  {
    field: 'anotherField',
    label: '自定义标签'  // 自定义标签
  }
]
```