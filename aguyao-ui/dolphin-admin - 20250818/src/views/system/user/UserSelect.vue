<template>
  <Dialog v-model="dialogVisible" title="选择用户" width="800">
    <ContentWrap>
      <!-- 搜索工作栏 -->
      <el-form
        class="-mb-15px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="68px"
      >
        <el-form-item label="用户名称" prop="username">
          <el-input
            v-model="queryParams.username"
            placeholder="请输入用户名称"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery"><Icon icon="ep:search" />搜索</el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" />重置</el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>

    <!-- 列表 -->
    <ContentWrap>
      <el-table v-loading="loading" :data="list" @current-change="handleCurrentChange">
        <!-- <el-table-column type="index" width="55" label="序号" /> -->
        <el-table-column label="选择" width="55" align="center">
          <template #default="{ row }">
            <el-radio v-model="selectedUserId" :label="row.id" @change="handleRadioChange(row)">&nbsp;</el-radio>
          </template>
        </el-table-column>
        <el-table-column label="用户编号" align="center" prop="id" />
        <el-table-column label="用户名称" align="center" prop="username" />
        <el-table-column label="用户昵称" align="center" prop="nickname" />
        <el-table-column label="部门" align="center" prop="deptName" />
        <el-table-column label="手机号码" align="center" prop="mobile" />
      </el-table>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </ContentWrap>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted } from 'vue'
import * as UserApi from '@/api/system/user'

defineOptions({ name: 'UserSelect' })

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  username: undefined
})
const queryFormRef = ref()
const selectedUserId = ref() // 选中的用户ID
const selectedUser = ref() // 选中的用户对象

const emit = defineEmits(['confirm'])

const dialogVisible = ref(false)

const open = () => {
  dialogVisible.value = true
  // 重置选择状态
  selectedUserId.value = undefined
  selectedUser.value = undefined
  getList()
}

defineExpose({ open })

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await UserApi.getUserPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

/** 单选框选中数据 */
const handleRadioChange = (row) => {
  selectedUser.value = row
}

/** 表格行点击事件 */
const handleCurrentChange = (row) => {
  if (row) {
    selectedUserId.value = row.id
    selectedUser.value = row
  }
}

const handleConfirm = () => {
  if (!selectedUser.value) {
    ElMessage.warning('请选择一个用户')
    return
  }
  emit('confirm', selectedUser.value)
  dialogVisible.value = false
}

/** 初始化 */
onMounted(() => {
  // 弹窗打开时再获取数据
})
</script>
