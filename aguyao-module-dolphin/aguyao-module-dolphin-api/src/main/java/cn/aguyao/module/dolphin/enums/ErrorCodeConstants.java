package cn.aguyao.module.dolphin.enums;

import cn.aguyao.framework.common.exception.ErrorCode;

/**
 * dolphin 错误码枚举类
 * <p>
 * dolphin 系统，使用 1-005-001-000 段
 */
public interface ErrorCodeConstants {



    //========== wsapp 1-005-001-000 ==========
    ErrorCode ACCOUNT_NOT_EXISTS = new ErrorCode(1_004_013_000, "账号不存在");
    ErrorCode RECHARGE_RECORD_NOT_EXISTS = new ErrorCode(1_004_013_001, "充值记录不存在");
    ErrorCode NUM_SEGMENT_NOT_EXISTS = new ErrorCode(1_004_013_002, "号码段不存在");
    ErrorCode PERSONAL_CENTER_NOT_EXISTS = new ErrorCode(1_004_013_003, "个人中心不存在");
    ErrorCode NUMBER_GENERATE_RECORD_NOT_EXISTS = new ErrorCode(1_004_013_004, "号码生成记录不存在");
    ErrorCode NUMBER_NOT_EXISTS = new ErrorCode(1_004_013_005, "号码不存在");
    ErrorCode COUNTRY_NOT_EXISTS = new ErrorCode(1_004_013_006, "国家不存在");
    ErrorCode CITY_NOT_EXISTS = new ErrorCode(1_004_013_007, "城市不存在");
    ErrorCode CONSUM_RECORD_NOT_EXISTS = new ErrorCode(1_004_013_008, "消费记录不存在");
    ErrorCode NUMBER_DETAIL_NOT_EXISTS = new ErrorCode(1_004_013_009, "号码详情不存在");
}
