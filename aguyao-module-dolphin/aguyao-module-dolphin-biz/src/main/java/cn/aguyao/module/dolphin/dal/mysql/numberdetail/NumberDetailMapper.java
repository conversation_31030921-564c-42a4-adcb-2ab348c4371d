package cn.aguyao.module.dolphin.dal.mysql.numberdetail;


import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.module.dolphin.dal.dataobject.numberdetail.NumberDetailDO;
import org.apache.ibatis.annotations.Mapper;
import cn.aguyao.module.dolphin.controller.admin.numberdetail.vo.*;

/**
 * 任务详情，手机号列 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface NumberDetailMapper extends BaseMapperX<NumberDetailDO> {

    default PageResult<NumberDetailDO> selectPage(NumberDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<NumberDetailDO>()
                .eqIfPresent(NumberDetailDO::getGencId, reqVO.getGencId())
                .eqIfPresent(NumberDetailDO::getMobile, reqVO.getMobile())
                .orderByDesc(NumberDetailDO::getId));
    }

}