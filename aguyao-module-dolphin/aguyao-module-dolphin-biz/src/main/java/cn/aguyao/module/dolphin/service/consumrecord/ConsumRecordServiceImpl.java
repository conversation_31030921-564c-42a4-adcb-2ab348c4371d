package cn.aguyao.module.dolphin.service.consumrecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.dolphin.controller.admin.consumrecord.vo.ConsumRecordPageReqVO;
import cn.aguyao.module.dolphin.controller.admin.consumrecord.vo.ConsumRecordSaveReqVO;
import cn.aguyao.module.dolphin.dal.dataobject.consumrecord.ConsumRecordDO;
import cn.aguyao.module.dolphin.dal.mysql.consumrecord.ConsumRecordMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.util.Objects;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.dolphin.enums.ErrorCodeConstants.CONSUM_RECORD_NOT_EXISTS;

/**
 * 消费记录主表，存储任务消费相关信息及通用基础字段 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ConsumRecordServiceImpl implements ConsumRecordService {

    @Resource
    private ConsumRecordMapper consumRecordMapper;

    @Override
    public Long createConsumRecord(ConsumRecordSaveReqVO createReqVO) {
        // 插入
        ConsumRecordDO consumRecord = BeanUtils.toBean(createReqVO, ConsumRecordDO.class);
        consumRecordMapper.insert(consumRecord);
        // 返回
        return consumRecord.getId();
    }

    @Override
    public Long createConsumRecord(ConsumRecordDO createReqVO) {
        if (Objects.isNull(createReqVO)) {
            return 0L;
        }
        consumRecordMapper.insert(createReqVO);
        return createReqVO.getId();
    }

    @Override
    public void updateConsumRecord(ConsumRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateConsumRecordExists(updateReqVO.getId());
        // 更新
        ConsumRecordDO updateObj = BeanUtils.toBean(updateReqVO, ConsumRecordDO.class);
        consumRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteConsumRecord(Long id) {
        // 校验存在
        validateConsumRecordExists(id);
        // 删除
        consumRecordMapper.deleteById(id);
    }

    private void validateConsumRecordExists(Long id) {
        if (consumRecordMapper.selectById(id) == null) {
            throw exception(CONSUM_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public ConsumRecordDO getConsumRecord(Long id) {
        return consumRecordMapper.selectById(id);
    }

    @Override
    public PageResult<ConsumRecordDO> getConsumRecordPage(ConsumRecordPageReqVO pageReqVO) {
        return consumRecordMapper.selectPage(pageReqVO);
    }

}