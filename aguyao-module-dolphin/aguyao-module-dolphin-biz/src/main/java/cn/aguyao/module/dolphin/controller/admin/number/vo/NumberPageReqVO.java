package cn.aguyao.module.dolphin.controller.admin.number.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.aguyao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.aguyao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 号码分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NumberPageReqVO extends PageParam {

    @Schema(description = "号段id", example = "7346")
    private Long segmentId;

    @Schema(description = "号段名称", example = "010")
    private String segmentName;

    @Schema(description = "号码，存在000的情况")
    private String number;

    @Schema(description = "序列号起始值", example = "0000")
    private String snFrom;

    @Schema(description = "运营商", example = "中国移动")
    private String operator;

    @Schema(description = "序列号结束值", example = "9999")
    private String snTo;

    @Schema(description = "序列号", example = "1234")
    private String snNumber;

    @Schema(description = "区号", example = "010")
    private String areaCode;

    @Schema(description = "位数", example = "11")
    private Integer digit;

    @Schema(description = "描述", example = "你猜")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}