package cn.aguyao.module.dolphin.controller.admin.numberdetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 任务详情，手机号列新增/修改 Request VO")
@Data
public class NumberDetailSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "32298")
    private Long id;

    @Schema(description = "任务的id，外键", example = "11299")
    private Long gencId;

    @Schema(description = "手机号")
    private String mobile;

}