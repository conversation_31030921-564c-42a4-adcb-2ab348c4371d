package cn.aguyao.module.dolphin.controller.admin.numberdetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 任务详情，手机号列 Response VO")
@Data
@ExcelIgnoreUnannotated
public class NumberDetailRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "32298")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "任务的id，外键", example = "11299")
    @ExcelProperty("任务的id，外键")
    private Long gencId;

    @Schema(description = "手机号")
    @ExcelProperty("手机号")
    private String mobile;

}