package cn.aguyao.module.dolphin.dal.mysql.city;

import java.util.*;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.module.dolphin.dal.dataobject.city.CityDO;
import org.apache.ibatis.annotations.Mapper;
import cn.aguyao.module.dolphin.controller.admin.city.vo.*;

/**
 * 城市 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CityMapper extends BaseMapperX<CityDO> {

    default PageResult<CityDO> selectPage(CityPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CityDO>()
                .eqIfPresent(CityDO::getCountryId, reqVO.getCountryId())
                .likeIfPresent(CityDO::getCountryName, reqVO.getCountryName())
                .likeIfPresent(CityDO::getName, reqVO.getName())
                .eqIfPresent(CityDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(CityDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(CityDO::getId));
    }

}