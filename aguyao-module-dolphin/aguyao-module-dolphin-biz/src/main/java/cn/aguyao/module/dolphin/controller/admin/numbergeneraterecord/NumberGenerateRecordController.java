package cn.aguyao.module.dolphin.controller.admin.numbergeneraterecord;

import cn.aguyao.framework.common.util.date.DateUtils;
import cn.aguyao.module.dolphin.controller.admin.numberdetail.vo.NumberDetailRespVO;
import cn.aguyao.module.dolphin.dal.mysql.numberdetail.NumberDetailMapper;
import cn.aguyao.module.dolphin.service.numberdetail.NumberDetailService;
import cn.hutool.core.date.DateUtil;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

import cn.aguyao.framework.excel.core.util.ExcelUtils;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.aguyao.module.dolphin.controller.admin.numbergeneraterecord.vo.*;
import cn.aguyao.module.dolphin.dal.dataobject.numbergeneraterecord.NumberGenerateRecordDO;
import cn.aguyao.module.dolphin.service.numbergeneraterecord.NumberGenerateRecordService;

@Tag(name = "管理后台 - 号码生成任务记录")
@RestController
@RequestMapping("/busi/number-generate-record")
@Validated
public class NumberGenerateRecordController {

    @Resource
    private NumberGenerateRecordService numberGenerateRecordService;

    @Resource
    private NumberDetailService numberDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建号码生成任务记录")
    @PreAuthorize("@ss.hasPermission('busi:number-generate-record:create')")
    public CommonResult<Long> createNumberGenerateRecord(@Valid @RequestBody NumberGenerateRecordSaveReqVO createReqVO) {
        return success(numberGenerateRecordService.createNumberGenerateRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新号码生成任务记录")
    @PreAuthorize("@ss.hasPermission('busi:number-generate-record:update')")
    public CommonResult<Boolean> updateNumberGenerateRecord(@Valid @RequestBody NumberGenerateRecordSaveReqVO updateReqVO) {
        numberGenerateRecordService.updateNumberGenerateRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除号码生成任务记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('busi:number-generate-record:delete')")
    public CommonResult<Boolean> deleteNumberGenerateRecord(@RequestParam("id") Long id) {
        numberGenerateRecordService.deleteNumberGenerateRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得号码生成任务记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('busi:number-generate-record:query')")
    public CommonResult<NumberGenerateRecordRespVO> getNumberGenerateRecord(@RequestParam("id") Long id) {
        NumberGenerateRecordDO numberGenerateRecord = numberGenerateRecordService.getNumberGenerateRecord(id);
        return success(BeanUtils.toBean(numberGenerateRecord, NumberGenerateRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得号码生成任务记录分页")
    @PreAuthorize("@ss.hasPermission('busi:number-generate-record:query')")
    public CommonResult<PageResult<NumberGenerateRecordRespVO>> getNumberGenerateRecordPage(@Valid NumberGenerateRecordPageReqVO pageReqVO) {
        PageResult<NumberGenerateRecordDO> pageResult = numberGenerateRecordService.getNumberGenerateRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, NumberGenerateRecordRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出号码生成任务记录 Excel")
    @PreAuthorize("@ss.hasPermission('busi:number-generate-record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportNumberGenerateRecordExcel(@Valid NumberGenerateRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<NumberGenerateRecordDO> list = numberGenerateRecordService.getNumberGenerateRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "号码生成任务记录.xls", "数据", NumberGenerateRecordRespVO.class,
                        BeanUtils.toBean(list, NumberGenerateRecordRespVO.class));
    }


    @GetMapping("/export-data")
    @Operation(summary = "导出号码生成任务记录 Excel")
    @PreAuthorize("@ss.hasPermission('busi:number-generate-record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportData(@RequestParam("id") Long id, HttpServletResponse response) throws IOException {

        NumberGenerateRecordDO numberGenerateRecord = numberGenerateRecordService.getNumberGenerateRecord(id);
        if (Objects.nonNull(numberGenerateRecord)) {
            List<NumberDetailRespVO> list = numberDetailService.getDetailsById(id);

            String datetime = DateUtil.format(new Date(), "yyyyMMddHHmmss");
            String name = datetime + "_" + numberGenerateRecord.getCityName() + "_" + numberGenerateRecord.getBatchCode()+".txt";
            // 导出 Excel
            ExcelUtils.write(response, "号码生成任务记录.xls", "数据", NumberGenerateRecordRespVO.class,
                    BeanUtils.toBean(list, NumberGenerateRecordRespVO.class));
        }
    }

}