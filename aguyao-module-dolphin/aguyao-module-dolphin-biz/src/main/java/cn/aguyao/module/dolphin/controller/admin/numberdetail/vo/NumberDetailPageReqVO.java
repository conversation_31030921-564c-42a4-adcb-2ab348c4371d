package cn.aguyao.module.dolphin.controller.admin.numberdetail.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.aguyao.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 任务详情，手机号列分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NumberDetailPageReqVO extends PageParam {

    @Schema(description = "任务的id，外键", example = "11299")
    private Long gencId;

    @Schema(description = "手机号")
    private String mobile;

}