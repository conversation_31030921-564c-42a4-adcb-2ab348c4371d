package cn.aguyao.module.dolphin.controller.admin.numsegment.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.aguyao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.aguyao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 地区号段分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NumSegmentPageReqVO extends PageParam {

    @Schema(description = "城市id", example = "11222")
    private Long cityId;

    @Schema(description = "城市名称", example = "北京")
    private String cityName;

    @Schema(description = "地区号段")
    private String numSegment;

    @Schema(description = "序列号起始值", example = "0000")
    private String snFrom;

    @Schema(description = "描述", example = "你说的对")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "地区名称", example = "地区名称")
    private String area;

    @Schema(description = "地区编号", example = "地区编号")
    private String areaCode;
}