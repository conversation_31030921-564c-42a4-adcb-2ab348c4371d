package cn.aguyao.module.dolphin.controller.admin.number.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 号码新增/修改 Request VO")
@Data
public class NumberSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "6660")
    private Long id;

    @Schema(description = "号段id", example = "7346")
    private Long segmentId;

    @Schema(description = "号段名称", example = "010")
    private String segmentName;

    @Schema(description = "号码，存在000的情况")
    private String number;

    @Schema(description = "序列号起始值", example = "0000")
    private String snFrom;

    @Schema(description = "运营商", example = "中国移动")
    private String operator;

    @Schema(description = "序列号结束值", example = "9999")
    private String snTo;

    @Schema(description = "序列号", example = "1234")
    private String snNumber;

    @Schema(description = "区号", example = "010")
    private String areaCode;

    @Schema(description = "位数", example = "11")
    private Integer digit;

    @Schema(description = "描述", example = "你猜")
    private String remark;

}