package cn.aguyao.module.dolphin.dal.mysql.number;

import java.util.*;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.module.dolphin.dal.dataobject.number.NumberDO;
import org.apache.ibatis.annotations.Mapper;
import cn.aguyao.module.dolphin.controller.admin.number.vo.*;

/**
 * 号码 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface NumberMapper extends BaseMapperX<NumberDO> {

    default PageResult<NumberDO> selectPage(NumberPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<NumberDO>()
                .eqIfPresent(NumberDO::getSegmentId, reqVO.getSegmentId())
                .likeIfPresent(NumberDO::getSegmentName, reqVO.getSegmentName())
                .eqIfPresent(NumberDO::getNumber, reqVO.getNumber())
                .eqIfPresent(NumberDO::getSnFrom, reqVO.getSnFrom())
                .likeIfPresent(NumberDO::getOperator, reqVO.getOperator())
                .eqIfPresent(NumberDO::getSnTo, reqVO.getSnTo())
                .eqIfPresent(NumberDO::getSnNumber, reqVO.getSnNumber())
                .eqIfPresent(NumberDO::getAreaCode, reqVO.getAreaCode())
                .eqIfPresent(NumberDO::getDigit, reqVO.getDigit())
                .eqIfPresent(NumberDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(NumberDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(NumberDO::getId));
    }

}