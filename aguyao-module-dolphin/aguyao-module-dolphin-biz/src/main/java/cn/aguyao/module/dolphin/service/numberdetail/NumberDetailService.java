package cn.aguyao.module.dolphin.service.numberdetail;

import java.util.*;
import javax.validation.*;
import cn.aguyao.module.dolphin.controller.admin.numberdetail.vo.*;
import cn.aguyao.module.dolphin.dal.dataobject.numberdetail.NumberDetailDO;
import cn.aguyao.framework.common.pojo.PageResult;

/**
 * 任务详情，手机号列 Service 接口
 *
 * <AUTHOR>
 */
public interface NumberDetailService {

    /**
     * 创建任务详情，手机号列
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createNumberDetail(@Valid NumberDetailSaveReqVO createReqVO);

    /**
     * 更新任务详情，手机号列
     *
     * @param updateReqVO 更新信息
     */
    void updateNumberDetail(@Valid NumberDetailSaveReqVO updateReqVO);

    /**
     * 删除任务详情，手机号列
     *
     * @param id 编号
     */
    void deleteNumberDetail(Long id);

    /**
     * 获得任务详情，手机号列
     *
     * @param id 编号
     * @return 任务详情，手机号列
     */
    NumberDetailDO getNumberDetail(Long id);

    /**
     * 获得任务详情，手机号列分页
     *
     * @param pageReqVO 分页查询
     * @return 任务详情，手机号列分页
     */
    PageResult<NumberDetailDO> getNumberDetailPage(NumberDetailPageReqVO pageReqVO);

    List<NumberDetailDO> getDetailsById(Long gencId);
}