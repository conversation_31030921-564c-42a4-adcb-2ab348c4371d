package cn.aguyao.module.dolphin.service.numberdetail;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import cn.aguyao.module.dolphin.controller.admin.numberdetail.vo.*;
import cn.aguyao.module.dolphin.dal.dataobject.numberdetail.NumberDetailDO;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;

import cn.aguyao.module.dolphin.dal.mysql.numberdetail.NumberDetailMapper;

import java.util.Collections;
import java.util.List;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.dolphin.enums.ErrorCodeConstants.NUMBER_DETAIL_NOT_EXISTS;

/**
 * 任务详情，手机号列 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class NumberDetailServiceImpl implements NumberDetailService {

    @Resource
    private NumberDetailMapper numberDetailMapper;

    @Override
    public Long createNumberDetail(NumberDetailSaveReqVO createReqVO) {
        // 插入
        NumberDetailDO numberDetail = BeanUtils.toBean(createReqVO, NumberDetailDO.class);
        numberDetailMapper.insert(numberDetail);
        // 返回
        return numberDetail.getId();
    }

    @Override
    public void updateNumberDetail(NumberDetailSaveReqVO updateReqVO) {
        // 校验存在
        validateNumberDetailExists(updateReqVO.getId());
        // 更新
        NumberDetailDO updateObj = BeanUtils.toBean(updateReqVO, NumberDetailDO.class);
        numberDetailMapper.updateById(updateObj);
    }

    @Override
    public void deleteNumberDetail(Long id) {
        // 校验存在
        validateNumberDetailExists(id);
        // 删除
        numberDetailMapper.deleteById(id);
    }

    private void validateNumberDetailExists(Long id) {
        if (numberDetailMapper.selectById(id) == null) {
            throw exception(NUMBER_DETAIL_NOT_EXISTS);
        }
    }

    @Override
    public NumberDetailDO getNumberDetail(Long id) {
        return numberDetailMapper.selectById(id);
    }

    @Override
    public PageResult<NumberDetailDO> getNumberDetailPage(NumberDetailPageReqVO pageReqVO) {
        return numberDetailMapper.selectPage(pageReqVO);
    }

    @Override
    public List<NumberDetailDO> getDetailsById(Long gencId) {
        return numberDetailMapper.selectList(NumberDetailDO::getGencId, gencId);
    }

}