package cn.aguyao.module.dolphin.dal.dataobject.numsegment;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.aguyao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 地区号段 DO
 *
 * <AUTHOR>
 */
@TableName("busi_num_segment")
@KeySequence("busi_num_segment_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NumSegmentDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 城市id
     */
    private Long cityId;
    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 地区号段
     */
    private String numSegment;
    /**
     * 描述
     */
    private String remark;



}