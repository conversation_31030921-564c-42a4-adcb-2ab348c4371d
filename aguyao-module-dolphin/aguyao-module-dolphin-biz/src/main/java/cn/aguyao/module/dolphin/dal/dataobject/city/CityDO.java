package cn.aguyao.module.dolphin.dal.dataobject.city;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.aguyao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 城市 DO
 *
 * <AUTHOR>
 */
@TableName("busi_city")
@KeySequence("busi_city_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CityDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 国家id
     */
    private Long countryId;
    /**
     * 国家名称
     */
    private String countryName;
    /**
     * 城市名称
     */
    private String name;
    /**
     * 描述
     */
    private String remark;

}