package cn.aguyao.module.dolphin.dal.mysql.numsegment;

import java.util.*;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.module.dolphin.dal.dataobject.numsegment.NumSegmentDO;
import org.apache.ibatis.annotations.Mapper;
import cn.aguyao.module.dolphin.controller.admin.numsegment.vo.*;

/**
 * 地区号段 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface NumSegmentMapper extends BaseMapperX<NumSegmentDO> {

    default PageResult<NumSegmentDO> selectPage(NumSegmentPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<NumSegmentDO>()
                .eqIfPresent(NumSegmentDO::getCityId, reqVO.getCityId())
                .likeIfPresent(NumSegmentDO::getCityName, reqVO.getCityName())
                .eqIfPresent(NumSegmentDO::getNumSegment, reqVO.getNumSegment())
                .eqIfPresent(NumSegmentDO::getSnFrom, reqVO.getSnFrom())
                .eqIfPresent(NumSegmentDO::getRemark, reqVO.getRemark())
                .eqIfPresent(NumSegmentDO::getArea, reqVO.getArea())
                .eqIfPresent(NumSegmentDO::getAreaCode, reqVO.getAreaCode())
                .betweenIfPresent(NumSegmentDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(NumSegmentDO::getId));
    }

}