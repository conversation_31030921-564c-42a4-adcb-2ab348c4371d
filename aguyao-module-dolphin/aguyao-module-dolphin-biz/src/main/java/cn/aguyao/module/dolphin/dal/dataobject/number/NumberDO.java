package cn.aguyao.module.dolphin.dal.dataobject.number;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.aguyao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 号码 DO
 *
 * <AUTHOR>
 */
@TableName("busi_number")
@KeySequence("busi_number_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NumberDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 号段id
     */
    private Long segmentId;
    /**
     * 号段名称
     */
    private String segmentName;

    /**
     * 号码，存在000的情况
     */
    private String number;
    /**
     * 序列号起始值
     */
    private String snFrom;
    /**
     * 运营商
     */
    private String operator;
    /**
     * 序列号结束值
     */
    private String snTo;
    /**
     * 序列号
     */
    private String snNumber;
    /**
     * 区号
     */
    private String area;
    /**
     * 区号
     */
    private String areaCode;
    /**
     * 位数
     */
    private Integer digit;
    /**
     * 描述
     */
    private String remark;

}