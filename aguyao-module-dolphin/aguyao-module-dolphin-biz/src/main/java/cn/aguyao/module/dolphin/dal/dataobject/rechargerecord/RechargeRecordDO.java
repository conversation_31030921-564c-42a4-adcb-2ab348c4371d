package cn.aguyao.module.dolphin.dal.dataobject.rechargerecord;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.aguyao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 充值记录 DO
 *
 * <AUTHOR>
 */
@TableName("busi_recharge_record")
@KeySequence("busi_recharge_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RechargeRecordDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 充值金额
     */
    private BigDecimal rechargeAmt;
    /**
     * 充值前金额
     */
    private BigDecimal beforeRechargeBalance;
    /**
     * 充值后金额
     */
    private BigDecimal afterRechargeBalance;
    /**
     * 充值凭证
     */
    private String voucher;

}