package cn.aguyao.module.dolphin.controller.admin.numsegment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 地区号段新增/修改 Request VO")
@Data
public class NumSegmentSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "16419")
    private Long id;

    @Schema(description = "城市id", example = "11222")
    private Long cityId;

    @Schema(description = "城市名称", example = "北京")
    private String cityName;

    @Schema(description = "地区号段")
    private String numSegment;

    @Schema(description = "序列号起始值", example = "0000")
    private String snFrom;

    @Schema(description = "描述", example = "你说的对")
    private String remark;

    @Schema(description = "地区名称", example = "地区名称")
    private String area;

    @Schema(description = "地区编号", example = "地区编号")
    private String areaCode;

}