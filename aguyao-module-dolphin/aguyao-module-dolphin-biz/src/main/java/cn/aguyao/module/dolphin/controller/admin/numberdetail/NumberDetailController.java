package cn.aguyao.module.dolphin.controller.admin.numberdetail;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

import cn.aguyao.framework.excel.core.util.ExcelUtils;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.aguyao.module.dolphin.controller.admin.numberdetail.vo.*;
import cn.aguyao.module.dolphin.dal.dataobject.numberdetail.NumberDetailDO;
import cn.aguyao.module.dolphin.service.numberdetail.NumberDetailService;

@Tag(name = "管理后台 - 任务详情，手机号列")
@RestController
@RequestMapping("/busi/number-detail")
@Validated
public class NumberDetailController {

    @Resource
    private NumberDetailService numberDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建任务详情，手机号列")
    @PreAuthorize("@ss.hasPermission('busi:number-detail:create')")
    public CommonResult<Long> createNumberDetail(@Valid @RequestBody NumberDetailSaveReqVO createReqVO) {
        return success(numberDetailService.createNumberDetail(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新任务详情，手机号列")
    @PreAuthorize("@ss.hasPermission('busi:number-detail:update')")
    public CommonResult<Boolean> updateNumberDetail(@Valid @RequestBody NumberDetailSaveReqVO updateReqVO) {
        numberDetailService.updateNumberDetail(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除任务详情，手机号列")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('busi:number-detail:delete')")
    public CommonResult<Boolean> deleteNumberDetail(@RequestParam("id") Long id) {
        numberDetailService.deleteNumberDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得任务详情，手机号列")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('busi:number-detail:query')")
    public CommonResult<NumberDetailRespVO> getNumberDetail(@RequestParam("id") Long id) {
        NumberDetailDO numberDetail = numberDetailService.getNumberDetail(id);
        return success(BeanUtils.toBean(numberDetail, NumberDetailRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得任务详情，手机号列分页")
    @PreAuthorize("@ss.hasPermission('busi:number-detail:query')")
    public CommonResult<PageResult<NumberDetailRespVO>> getNumberDetailPage(@Valid NumberDetailPageReqVO pageReqVO) {
        PageResult<NumberDetailDO> pageResult = numberDetailService.getNumberDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, NumberDetailRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出任务详情，手机号列 Excel")
    @PreAuthorize("@ss.hasPermission('busi:number-detail:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportNumberDetailExcel(@Valid NumberDetailPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<NumberDetailDO> list = numberDetailService.getNumberDetailPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "任务详情，手机号列.xls", "数据", NumberDetailRespVO.class,
                        BeanUtils.toBean(list, NumberDetailRespVO.class));
    }

}