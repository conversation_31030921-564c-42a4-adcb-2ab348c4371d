package cn.aguyao.module.dolphin.controller.admin.city.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 城市 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CityRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "20551")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "国家id", example = "2415")
    @ExcelProperty("国家id")
    private Long countryId;

    @Schema(description = "国家名称", example = "中国")
    @ExcelProperty("国家名称")
    private String countryName;

    @Schema(description = "城市名称", example = "赵六")
    @ExcelProperty("城市名称")
    private String name;

    @Schema(description = "描述", example = "你说的对")
    @ExcelProperty("描述")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}