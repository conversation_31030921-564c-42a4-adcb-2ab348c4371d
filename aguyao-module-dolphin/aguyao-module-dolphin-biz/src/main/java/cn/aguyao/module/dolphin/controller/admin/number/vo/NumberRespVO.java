package cn.aguyao.module.dolphin.controller.admin.number.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 号码 Response VO")
@Data
@ExcelIgnoreUnannotated
public class NumberRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "6660")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "号段id", example = "7346")
    @ExcelProperty("号段id")
    private Long segmentId;

    @Schema(description = "号段名称", example = "010")
    @ExcelProperty("号段名称")
    private String segmentName;

    @Schema(description = "号码，存在000的情况")
    @ExcelProperty("号码，存在000的情况")
    private String number;

    @Schema(description = "序列号起始值", example = "0000")
    @ExcelProperty("序列号起始值")
    private String snFrom;

    @Schema(description = "运营商", example = "中国移动")
    @ExcelProperty("运营商")
    private String operator;

    @Schema(description = "序列号结束值", example = "9999")
    @ExcelProperty("序列号结束值")
    private String snTo;

    @Schema(description = "序列号", example = "1234")
    @ExcelProperty("序列号")
    private String snNumber;

    @Schema(description = "区号", example = "010")
    @ExcelProperty("区号")
    private String area;

    @Schema(description = "区号", example = "010")
    @ExcelProperty("区号")
    private String areaCode;

    @Schema(description = "位数", example = "11")
    @ExcelProperty("位数")
    private Integer digit;

    @Schema(description = "描述", example = "你猜")
    @ExcelProperty("描述")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}