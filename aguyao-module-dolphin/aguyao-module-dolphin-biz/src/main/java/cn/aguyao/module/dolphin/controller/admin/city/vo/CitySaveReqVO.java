package cn.aguyao.module.dolphin.controller.admin.city.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 城市新增/修改 Request VO")
@Data
public class CitySaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "20551")
    private Long id;

    @Schema(description = "国家id", example = "2415")
    private Long countryId;

    @Schema(description = "国家名称", example = "中国")
    private String countryName;

    @Schema(description = "城市名称", example = "赵六")
    private String name;

    @Schema(description = "描述", example = "你说的对")
    private String remark;

}