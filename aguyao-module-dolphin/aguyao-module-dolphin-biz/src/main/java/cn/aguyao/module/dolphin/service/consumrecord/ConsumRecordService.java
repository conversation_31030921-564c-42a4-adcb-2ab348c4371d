package cn.aguyao.module.dolphin.service.consumrecord;

import java.util.*;
import javax.validation.*;
import cn.aguyao.module.dolphin.controller.admin.consumrecord.vo.*;
import cn.aguyao.module.dolphin.dal.dataobject.consumrecord.ConsumRecordDO;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.pojo.PageParam;

/**
 * 消费记录主表，存储任务消费相关信息及通用基础字段 Service 接口
 *
 * <AUTHOR>
 */
public interface ConsumRecordService {

    /**
     * 创建消费记录主表，存储任务消费相关信息及通用基础字段
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createConsumRecord(@Valid ConsumRecordSaveReqVO createReqVO);

    Long createConsumRecord(ConsumRecordDO createReqVO);

    /**
     * 更新消费记录主表，存储任务消费相关信息及通用基础字段
     *
     * @param updateReqVO 更新信息
     */
    void updateConsumRecord(@Valid ConsumRecordSaveReqVO updateReqVO);

    /**
     * 删除消费记录主表，存储任务消费相关信息及通用基础字段
     *
     * @param id 编号
     */
    void deleteConsumRecord(Long id);

    /**
     * 获得消费记录主表，存储任务消费相关信息及通用基础字段
     *
     * @param id 编号
     * @return 消费记录主表，存储任务消费相关信息及通用基础字段
     */
    ConsumRecordDO getConsumRecord(Long id);

    /**
     * 获得消费记录主表，存储任务消费相关信息及通用基础字段分页
     *
     * @param pageReqVO 分页查询
     * @return 消费记录主表，存储任务消费相关信息及通用基础字段分页
     */
    PageResult<ConsumRecordDO> getConsumRecordPage(ConsumRecordPageReqVO pageReqVO);

}