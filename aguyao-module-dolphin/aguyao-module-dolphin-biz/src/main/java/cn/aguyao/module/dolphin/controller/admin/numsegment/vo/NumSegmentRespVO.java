package cn.aguyao.module.dolphin.controller.admin.numsegment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 地区号段 Response VO")
@Data
@ExcelIgnoreUnannotated
public class NumSegmentRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "16419")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "城市id", example = "11222")
    @ExcelProperty("城市id")
    private Long cityId;

    @Schema(description = "城市名称", example = "北京")
    @ExcelProperty("城市名称")
    private String cityName;

    @Schema(description = "地区号段")
    @ExcelProperty("地区号段")
    private String numSegment;

    @Schema(description = "序列号起始值", example = "0000")
    @ExcelProperty("序列号起始值")
    private String snFrom;

    @Schema(description = "描述", example = "你说的对")
    @ExcelProperty("描述")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "地区名称", example = "地区名称")
    private String area;

    @Schema(description = "地区编号", example = "地区编号")
    private String areaCode;

}