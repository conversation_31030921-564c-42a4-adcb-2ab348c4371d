package cn.aguyao.module.dolphin.dal.dataobject.numbergeneraterecord;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.aguyao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 号码生成任务记录 DO
 *
 * <AUTHOR>
 */
@TableName("busi_number_generate_record")
@KeySequence("busi_number_generate_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NumberGenerateRecordDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 任务类型，1：全球号码生成；2：自定义号码生成
     */
    private Integer taskType;
    /**
     * 批次编号
     */
    private String batchCode;
    /**
     * 数据总量
     */
    private Integer totalNum;
    /**
     * 生成数量
     */
    private Integer generalNum;
    /**
     * 国家简称，如：AD
     */
    private String countryCode;
    /**
     * 国家名称
     */
    private String countryName;
    /**
     * 城市编码
     */
    private String cityCode;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 区号
     */
    private String areaCode;
    /**
     * 区域名称
     */
    private String areaName;
    /**
     * 号段
     */
    private String numSegment;
    /**
     * 选择号码
     */
    private String selectNum;
    /**
     * 起始号码
     */
    private String startNumber;
    /**
     * 结束号码
     */
    private String endNumber;
    /**
     * 运营商
     */
    private String operator;
    /**
     * 描述
     */
    private String remark;

}