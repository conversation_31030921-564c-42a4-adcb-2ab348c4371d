package cn.aguyao.module.dolphin.service.rechargerecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.dolphin.controller.admin.rechargerecord.vo.RechargeRecordPageReqVO;
import cn.aguyao.module.dolphin.controller.admin.rechargerecord.vo.RechargeRecordSaveReqVO;
import cn.aguyao.module.dolphin.dal.dataobject.consumrecord.ConsumRecordDO;
import cn.aguyao.module.dolphin.dal.dataobject.rechargerecord.RechargeRecordDO;
import cn.aguyao.module.dolphin.dal.mysql.rechargerecord.RechargeRecordMapper;
import cn.aguyao.module.dolphin.service.consumrecord.ConsumRecordService;
import cn.aguyao.module.system.api.user.AdminUserApi;
import cn.aguyao.module.system.api.user.dto.AdminUserRespDTO;
import cn.hutool.core.util.RandomUtil;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.util.Objects;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.dolphin.enums.ErrorCodeConstants.RECHARGE_RECORD_NOT_EXISTS;

/**
 * 充值记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RechargeRecordServiceImpl implements RechargeRecordService {

    @Resource
    private RechargeRecordMapper rechargeRecordMapper;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private ConsumRecordService consumRecordService;

    @Override
    public Long createRechargeRecord(RechargeRecordSaveReqVO createReqVO) {
        // 插入
        RechargeRecordDO rechargeRecord = BeanUtils.toBean(createReqVO, RechargeRecordDO.class);

        AdminUserRespDTO user = adminUserApi.getUser(createReqVO.getUserId());
        if (Objects.nonNull(user)) {
            BigDecimal balance = user.getBalance() == null ? BigDecimal.ZERO : user.getBalance();
            rechargeRecord.setBeforeRechargeBalance(balance);
            balance = balance.add(createReqVO.getRechargeAmt());
            rechargeRecord.setAfterRechargeBalance(balance);
            rechargeRecord.setUserName(user.getNickname());
            // 更新余额
            adminUserApi.updateBalance(user.getId(), balance);
        }

        String taskId = System.currentTimeMillis() + RandomUtil.randomNumbers(5);

        rechargeRecordMapper.insert(rechargeRecord);

        // 充值
        ConsumRecordDO consumRecord = ConsumRecordDO.builder()
                .taskId(taskId)
                .username(user.getNickname())
                .consumeType("1")
                .consumeStatus(1)
                .taskType("1")
                .operationBeforeBalance(rechargeRecord.getBeforeRechargeBalance())
                .operationAfterBalance(rechargeRecord.getAfterRechargeBalance())
                .description("管理员充值").build();

        consumRecordService.createConsumRecord(consumRecord);

        // 返回
        return rechargeRecord.getId();
    }

    @Override
    public void updateRechargeRecord(RechargeRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateRechargeRecordExists(updateReqVO.getId());
        // 更新
        RechargeRecordDO updateObj = BeanUtils.toBean(updateReqVO, RechargeRecordDO.class);
        rechargeRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteRechargeRecord(Long id) {
        // 校验存在
//        validateRechargeRecordExists(id);
        RechargeRecordDO recordDO = rechargeRecordMapper.selectById(id);
        if ( recordDO == null) {
            throw exception(RECHARGE_RECORD_NOT_EXISTS);
        }

        // 扣减余额
        AdminUserRespDTO user = adminUserApi.getUser(recordDO.getUserId());
        if (Objects.nonNull(user)) {
            BigDecimal balance = user.getBalance() == null ? BigDecimal.ZERO : user.getBalance();
            balance = balance.subtract(recordDO.getRechargeAmt());
//            user.setBalance( balance);
            adminUserApi.updateBalance(recordDO.getUserId(), balance);
        }

        // 退款

        // 删除
        rechargeRecordMapper.deleteById(id);
    }

    private void validateRechargeRecordExists(Long id) {
        if (rechargeRecordMapper.selectById(id) == null) {
            throw exception(RECHARGE_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public RechargeRecordDO getRechargeRecord(Long id) {
        return rechargeRecordMapper.selectById(id);
    }

    @Override
    public PageResult<RechargeRecordDO> getRechargeRecordPage(RechargeRecordPageReqVO pageReqVO) {
        return rechargeRecordMapper.selectPage(pageReqVO);
    }

}